<?php
/**
 * 易教育下单直接测试脚本
 * 直接调用易教育API进行下单测试
 */

// 引入公共配置文件
include('confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育下单直接测试</h1>\n";
echo "<pre>\n";

echo "=== 易教育下单直接测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：获取易教育配置
echo "步骤1：获取易教育配置\n";
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
if (!$huoyuan) {
    die("❌ 未找到易教育货源配置\n");
}

echo "✅ 货源配置正常\n";
echo "   API地址: {$huoyuan['url']}\n";
echo "   账号: {$huoyuan['user']}\n";

// 步骤2：获取Token
echo "\n步骤2：获取访问Token\n";
$token = $huoyuan['token'];

if (empty($token)) {
    echo "Token为空，重新登录...\n";
    
    $login_data = array(
        "username" => $huoyuan["user"],
        "password" => $huoyuan["pass"]
    );

    $login_url = "{$huoyuan["url"]}/api/login";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $login_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($curl_error || $http_code != 200) {
        die("❌ 登录失败: {$curl_error} (HTTP: {$http_code})\n");
    }

    $login_result_array = json_decode($login_result, true);
    if (!$login_result_array || $login_result_array["code"] != 200) {
        die("❌ 登录失败: " . ($login_result_array["message"] ?? "未知错误") . "\n");
    }

    $token = $login_result_array["data"]["token"];
    echo "✅ 重新登录成功\n";
    
    // 更新Token到数据库
    $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$huoyuan['hid']}'");
} else {
    echo "✅ 使用缓存Token\n";
}

echo "Token: " . substr($token, 0, 30) . "...\n";

// 步骤3：获取测试项目
echo "\n步骤3：获取测试项目\n";
$project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
if (!$project) {
    die("❌ 未找到可用的易教育项目\n");
}

echo "✅ 找到测试项目\n";
echo "   项目名称: {$project['name']}\n";
echo "   项目编号: {$project['number']}\n";
echo "   是否需要查课: " . ($project['isSearchCourse'] == '1' ? '是' : '否') . "\n";

// 步骤4：直接测试下单API
echo "\n步骤4：直接测试下单API\n";

// 测试账号密码
$test_user = "testuser" . rand(1000, 9999);
$test_pass = "testpass" . rand(1000, 9999);

echo "测试账号: {$test_user}\n";
echo "测试密码: {$test_pass}\n";

// 构建下单数据
if ($project['isSearchCourse'] == '0') {
    // 无需查课的项目
    $order_data = array(
        "websiteNumber" => $project['number'],
        "data" => array(array(
            "username" => $test_user,
            "password" => $test_pass
        ))
    );
} else {
    // 需要查课的项目，使用简化的课程数据
    $order_data = array(
        "websiteNumber" => $project['number'],
        "data" => array(array(
            "username" => $test_user,
            "password" => $test_pass,
            "name" => $test_user . "----" . $test_pass,
            "children" => array(array(
                "name" => "测试课程",
                "disabled" => false,
                "id" => "test_course_" . rand(1000, 9999),
                "selected" => true
            )),
            "selected" => true
        ))
    );
}

$order_url = "{$huoyuan["url"]}/api/order/buy";

echo "下单URL: {$order_url}\n";
echo "下单数据: " . json_encode($order_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";

// 发送下单请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $order_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    "Content-Type: application/json",
    "Authorization: Bearer {$token}"
));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$start_time = microtime(true);
$order_result = curl_exec($ch);
$end_time = microtime(true);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$request_time = round($end_time - $start_time, 2);
echo "\n下单请求耗时: {$request_time}秒\n";
echo "HTTP状态码: {$http_code}\n";

if ($curl_error) {
    echo "❌ 下单请求失败: {$curl_error}\n";
} else {
    echo "下单响应内容: " . $order_result . "\n\n";
    
    $order_result_array = json_decode($order_result, true);
    if ($order_result_array) {
        echo "下单结果解析:\n";
        echo json_encode($order_result_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
        if ($order_result_array["code"] == 200) {
            echo "\n✅ 下单API调用成功！\n";
            
            // 尝试获取订单ID
            $order_id = null;
            
            if (isset($order_result_array["data"]["orderList"]) && is_array($order_result_array["data"]["orderList"])) {
                foreach ($order_result_array["data"]["orderList"] as $order) {
                    if (isset($order["orderId"]) && $order["username"] == $test_user) {
                        $order_id = $order["orderId"];
                        break;
                    }
                }
            } elseif (isset($order_result_array["data"]["orderId"])) {
                $order_id = $order_result_array["data"]["orderId"];
            }
            
            if ($order_id) {
                echo "✅ 获取到订单ID: {$order_id}\n";
                echo "✅ 易教育下单功能正常！\n";
            } else {
                echo "⚠️  下单成功但未获取到订单ID\n";
                echo "数据结构可能需要调整\n";
            }
            
        } else {
            echo "❌ 下单失败\n";
            echo "错误代码: {$order_result_array["code"]}\n";
            echo "错误信息: " . ($order_result_array["message"] ?? "未知错误") . "\n";
        }
    } else {
        echo "❌ 下单响应解析失败\n";
        echo "原始响应: " . substr($order_result, 0, 500) . "\n";
    }
}

echo "\n=== 测试完成 ===\n";

if (isset($order_result_array) && $order_result_array["code"] == 200) {
    echo "🎉 易教育下单功能测试通过！\n";
    echo "现在可以在前端正常使用易教育下单功能。\n";
} else {
    echo "❌ 易教育下单功能存在问题，请检查：\n";
    echo "1. API地址是否正确\n";
    echo "2. Token是否有效\n";
    echo "3. 项目编号是否存在\n";
    echo "4. 下单数据格式是否正确\n";
    echo "5. 网络连接是否正常\n";
}

echo "</pre>\n";
?>
