<?php
/**
 * 易教育查课直接测试脚本
 * 直接调用API文件进行测试
 */

// 引入公共配置文件
include('confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育查课直接测试</h1>\n";
echo "<pre>\n";

echo "=== 易教育查课直接测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：获取测试项目
echo "步骤1：获取测试项目\n";

$test_project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
if (!$test_project) {
    die("❌ 未找到易教育项目数据\n");
}

echo "✅ 找到测试项目\n";
echo "   项目编号: {$test_project['number']}\n";
echo "   项目名称: {$test_project['name']}\n";
echo "   是否需要查课: " . ($test_project['isSearchCourse'] == '1' ? '是' : '否') . "\n";

// 步骤2：模拟POST数据
echo "\n步骤2：模拟POST数据\n";

$_POST['id'] = $test_project['number'];
$_POST['user'] = 'testuser123';
$_POST['pass'] = 'testpass123';

echo "POST数据:\n";
echo "   id: {$_POST['id']}\n";
echo "   user: {$_POST['user']}\n";
echo "   pass: {$_POST['pass']}\n";

// 步骤3：模拟GET参数
echo "\n步骤3：模拟GET参数\n";

$_GET['act'] = 'getcourse';

echo "GET参数:\n";
echo "   act: {$_GET['act']}\n";

// 步骤4：直接包含并执行API文件
echo "\n步骤4：直接执行API文件\n";

// 开始输出缓冲
ob_start();

try {
    // 直接包含API文件
    include('api/jxjyapi.php');
    
    // 获取输出
    $api_output = ob_get_contents();
    
} catch (Exception $e) {
    $api_output = "异常: " . $e->getMessage();
} catch (Error $e) {
    $api_output = "错误: " . $e->getMessage();
}

// 结束输出缓冲
ob_end_clean();

echo "API执行结果:\n";
echo $api_output . "\n";

// 步骤5：解析结果
echo "\n步骤5：解析结果\n";

$result = json_decode($api_output, true);
if ($result && is_array($result)) {
    echo "结果解析成功:\n";
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    if (isset($result[0])) {
        $api_result = $result[0];
        
        if (isset($api_result['code'])) {
            if ($api_result['code'] == 1) {
                echo "\n✅ 查课API调用成功！\n";
                echo "返回消息: {$api_result['msg']}\n";
                
                if (isset($api_result['data']) && is_array($api_result['data'])) {
                    echo "课程数量: " . count($api_result['data']) . "\n";
                    if (count($api_result['data']) > 0) {
                        echo "示例课程:\n";
                        foreach (array_slice($api_result['data'], 0, 3) as $course) {
                            echo "   - {$course['name']}\n";
                        }
                    }
                }
            } else {
                echo "\n❌ 查课API调用失败\n";
                echo "错误代码: {$api_result['code']}\n";
                echo "错误信息: {$api_result['msg']}\n";
                
                if (strpos($api_result['msg'], '项目信息不存在') !== false) {
                    echo "⚠️  仍然是项目信息不存在错误\n";
                } else {
                    echo "✅ 不再是项目信息不存在错误，修复有效\n";
                }
            }
        }
    }
} else {
    echo "结果解析失败，原始输出:\n";
    echo $api_output . "\n";
}

// 步骤6：测试项目查询
echo "\n步骤6：验证项目查询\n";

$project_check = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$_POST['id']}' LIMIT 1");
if ($project_check) {
    echo "✅ 项目查询正常\n";
    echo "   查询到: {$project_check['name']}\n";
} else {
    echo "❌ 项目查询失败\n";
    echo "   查询条件: number = '{$_POST['id']}'\n";
}

echo "\n=== 测试完成 ===\n";

// 总结
if (isset($api_result) && $api_result['code'] == 1) {
    echo "🎉 易教育查课功能修复成功！\n";
    echo "现在前端查课应该可以正常工作了。\n";
} elseif (isset($api_result) && strpos($api_result['msg'], '项目信息不存在') === false) {
    echo "✅ 项目信息查询问题已修复\n";
    echo "查课失败可能是其他原因（如网络、账号密码等）\n";
} else {
    echo "❌ 修复可能不完整，请检查：\n";
    echo "1. API文件是否正确更新\n";
    echo "2. 项目数据是否正确同步\n";
    echo "3. 数据库查询是否正确\n";
}

echo "</pre>\n";
?>
