<?php
/**
 * 易教育API接口文件 - 测试版本
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 获取请求参数
$act = $_GET['act'] ?? $_GET['action'] ?? '';

// 获取易教育货源配置
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$huoyuan) {
    exit(json_encode(array("code" => -1, "msg" => "未找到易教育货源配置")));
}

// 处理查课请求
if ($act == 'getcourse') {
    $id = trim(strip_tags($_POST['id'] ?? ''));
    $username = trim(strip_tags($_POST['user'] ?? ''));
    $password = trim(strip_tags($_POST['pass'] ?? ''));
    
    if (empty($id) || empty($username) || empty($password)) {
        $responseData = array(array("code" => -1, "msg" => "参数不能为空"));
        exit(json_encode($responseData));
    }
    
    // 使用项目ID查询项目信息
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$id}' LIMIT 1");
    if (!$rs) {
        $responseData = array(array("code" => -1, "msg" => "项目信息不存在，项目ID: {$id}"));
        exit(json_encode($responseData));
    }
    
    // 检查是否需要查课
    if ($rs['isSearchCourse'] == '0') {
        $responseData = array(array("code" => -1, "msg" => "该项目无需查课"));
        exit(json_encode($responseData));
    }
    
    // 模拟查课成功（用于测试）
    $responseData = array(array(
        "code" => 1, 
        "msg" => "查课测试成功", 
        "data" => array(
            array("id" => "test_course_1", "name" => "测试课程1"),
            array("id" => "test_course_2", "name" => "测试课程2")
        ),
        "userinfo" => "测试用户信息"
    ));
    exit(json_encode($responseData));
}

// 处理项目列表请求
if ($act == 'jxjyclass') {
    $projects_result = $DB->query("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 ORDER BY id ASC LIMIT 10");
    $data = array();
    
    if ($projects_result) {
        while ($row = $DB->fetch_array($projects_result)) {
            $data[] = $row;
        }
    }
    
    $response = array('code' => 1, 'data' => $data);
    exit(json_encode($response));
}

// 默认响应
$result = array(
    "code" => 1,
    "msg" => "易教育API接口测试版本",
    "actions" => array(
        "jxjyclass" => "获取项目列表",
        "getcourse" => "查课接口"
    )
);
exit(json_encode($result));
?>
