<?php
include('confing/common.php');

echo "Testing API...\n";

// 模拟GET参数
$_GET['act'] = 'jxjyclass';

// 获取易教育货源配置
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$huoyuan) {
    echo "No huoyuan found\n";
    exit;
}

echo "Found huoyuan: " . $huoyuan['name'] . "\n";

// 获取项目列表
$projects_result = $DB->query("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 ORDER BY id ASC LIMIT 3");
$data = array();

if ($projects_result) {
    while ($row = $DB->fetch_array($projects_result)) {
        $data[] = $row;
    }
}

echo "Found " . count($data) . " projects\n";
if (count($data) > 0) {
    echo "First project: " . $data[0]['name'] . "\n";
}

$response = array('code' => 1, 'data' => $data);
echo json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
?>
