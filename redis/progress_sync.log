[2025-08-21 17:18:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:18:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:18:01] ✅ Redis连接成功
[2025-08-21 17:18:01] ✅ Redis连接成功
[2025-08-21 17:18:01] 当前进度同步队列长度: 0
[2025-08-21 17:18:01] 当前进度同步队列长度: 0
[2025-08-21 17:18:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于61分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于61分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于61分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于61分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于67分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于67分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于115分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于115分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于763分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于947分钟前)
[2025-08-21 17:18:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于947分钟前)
[2025-08-21 17:18:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:18:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:18:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:18:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:18:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:18:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:18:01] 最终进度同步队列长度: 9
[2025-08-21 17:18:01] 最终进度同步队列长度: 9
[2025-08-21 17:18:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:18:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:20:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:20:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:20:01] ✅ Redis连接成功
[2025-08-21 17:20:01] ✅ Redis连接成功
[2025-08-21 17:20:01] 当前进度同步队列长度: 0
[2025-08-21 17:20:01] 当前进度同步队列长度: 0
[2025-08-21 17:20:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于63分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于63分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于63分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于63分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于69分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于69分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于117分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于117分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于765分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于949分钟前)
[2025-08-21 17:20:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于949分钟前)
[2025-08-21 17:20:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:20:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:20:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:20:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:20:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:20:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:20:01] 最终进度同步队列长度: 9
[2025-08-21 17:20:01] 最终进度同步队列长度: 9
[2025-08-21 17:20:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:20:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:22:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:22:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:22:01] ✅ Redis连接成功
[2025-08-21 17:22:01] ✅ Redis连接成功
[2025-08-21 17:22:01] 当前进度同步队列长度: 0
[2025-08-21 17:22:01] 当前进度同步队列长度: 0
[2025-08-21 17:22:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于65分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于65分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于65分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于65分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于71分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于71分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于119分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于119分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于767分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于951分钟前)
[2025-08-21 17:22:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于951分钟前)
[2025-08-21 17:22:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:22:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:22:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:22:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:22:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:22:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:22:01] 最终进度同步队列长度: 11
[2025-08-21 17:22:01] 最终进度同步队列长度: 11
[2025-08-21 17:22:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:22:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:24:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:24:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:24:01] ✅ Redis连接成功
[2025-08-21 17:24:01] ✅ Redis连接成功
[2025-08-21 17:24:01] 当前进度同步队列长度: 0
[2025-08-21 17:24:01] 当前进度同步队列长度: 0
[2025-08-21 17:24:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于67分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于67分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于67分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于67分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于73分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于73分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于121分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于121分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于769分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于953分钟前)
[2025-08-21 17:24:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于953分钟前)
[2025-08-21 17:24:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:24:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:24:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:24:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:24:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:24:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:24:02] 最终进度同步队列长度: 9
[2025-08-21 17:24:02] 最终进度同步队列长度: 9
[2025-08-21 17:24:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:24:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:26:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:26:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:26:01] ✅ Redis连接成功
[2025-08-21 17:26:01] ✅ Redis连接成功
[2025-08-21 17:26:01] 当前进度同步队列长度: 0
[2025-08-21 17:26:01] 当前进度同步队列长度: 0
[2025-08-21 17:26:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于69分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于69分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于69分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于69分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于75分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于75分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于123分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于123分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于771分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于955分钟前)
[2025-08-21 17:26:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于955分钟前)
[2025-08-21 17:26:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:26:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:26:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:26:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:26:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:26:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:26:02] 最终进度同步队列长度: 9
[2025-08-21 17:26:02] 最终进度同步队列长度: 9
[2025-08-21 17:26:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:26:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:28:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:28:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:28:01] ✅ Redis连接成功
[2025-08-21 17:28:01] ✅ Redis连接成功
[2025-08-21 17:28:01] 当前进度同步队列长度: 3
[2025-08-21 17:28:01] 当前进度同步队列长度: 3
[2025-08-21 17:28:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于125分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于125分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于773分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于957分钟前)
[2025-08-21 17:28:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于957分钟前)
[2025-08-21 17:28:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:28:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:28:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:28:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:28:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:28:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:28:01] 最终进度同步队列长度: 9
[2025-08-21 17:28:01] 最终进度同步队列长度: 9
[2025-08-21 17:28:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:28:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:30:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:30:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:30:01] ✅ Redis连接成功
[2025-08-21 17:30:01] ✅ Redis连接成功
[2025-08-21 17:30:01] 当前进度同步队列长度: 0
[2025-08-21 17:30:01] 当前进度同步队列长度: 0
[2025-08-21 17:30:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于73分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于73分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于73分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于73分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于79分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于79分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于127分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于127分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于775分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于959分钟前)
[2025-08-21 17:30:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于959分钟前)
[2025-08-21 17:30:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:30:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:30:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:30:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:30:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:30:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:30:01] 最终进度同步队列长度: 9
[2025-08-21 17:30:01] 最终进度同步队列长度: 9
[2025-08-21 17:30:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:30:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:32:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:32:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:32:01] ✅ Redis连接成功
[2025-08-21 17:32:01] ✅ Redis连接成功
[2025-08-21 17:32:01] 当前进度同步队列长度: 0
[2025-08-21 17:32:01] 当前进度同步队列长度: 0
[2025-08-21 17:32:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于75分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于75分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于75分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于75分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于81分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于81分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于129分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于129分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于777分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于961分钟前)
[2025-08-21 17:32:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于961分钟前)
[2025-08-21 17:32:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:32:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:32:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:32:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:32:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:32:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:32:02] 最终进度同步队列长度: 9
[2025-08-21 17:32:02] 最终进度同步队列长度: 9
[2025-08-21 17:32:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:32:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:34:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:34:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:34:01] ✅ Redis连接成功
[2025-08-21 17:34:01] ✅ Redis连接成功
[2025-08-21 17:34:01] 当前进度同步队列长度: 0
[2025-08-21 17:34:01] 当前进度同步队列长度: 0
[2025-08-21 17:34:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于77分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于77分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于77分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于77分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于83分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于83分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于131分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于131分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于779分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于963分钟前)
[2025-08-21 17:34:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于963分钟前)
[2025-08-21 17:34:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:34:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:34:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:34:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:34:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:34:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:34:01] 最终进度同步队列长度: 9
[2025-08-21 17:34:01] 最终进度同步队列长度: 9
[2025-08-21 17:34:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:34:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:36:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:36:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:36:01] ✅ Redis连接成功
[2025-08-21 17:36:01] ✅ Redis连接成功
[2025-08-21 17:36:01] 当前进度同步队列长度: 0
[2025-08-21 17:36:01] 当前进度同步队列长度: 0
[2025-08-21 17:36:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于79分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于79分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于79分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于79分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于85分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于85分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于133分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于133分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于781分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于965分钟前)
[2025-08-21 17:36:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于965分钟前)
[2025-08-21 17:36:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:36:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:36:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:36:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:36:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:36:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:36:01] 最终进度同步队列长度: 9
[2025-08-21 17:36:01] 最终进度同步队列长度: 9
[2025-08-21 17:36:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:36:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:38:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:38:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:38:01] ✅ Redis连接成功
[2025-08-21 17:38:01] ✅ Redis连接成功
[2025-08-21 17:38:01] 当前进度同步队列长度: 3
[2025-08-21 17:38:01] 当前进度同步队列长度: 3
[2025-08-21 17:38:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于135分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于135分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于783分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于967分钟前)
[2025-08-21 17:38:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于967分钟前)
[2025-08-21 17:38:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:38:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:38:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:38:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:38:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:38:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:38:01] 最终进度同步队列长度: 9
[2025-08-21 17:38:01] 最终进度同步队列长度: 9
[2025-08-21 17:38:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:38:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:40:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:40:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:40:01] ✅ Redis连接成功
[2025-08-21 17:40:01] ✅ Redis连接成功
[2025-08-21 17:40:01] 当前进度同步队列长度: 3
[2025-08-21 17:40:01] 当前进度同步队列长度: 3
[2025-08-21 17:40:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于137分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于137分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于785分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于969分钟前)
[2025-08-21 17:40:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于969分钟前)
[2025-08-21 17:40:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:40:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:40:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:40:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:40:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:40:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:40:02] 最终进度同步队列长度: 9
[2025-08-21 17:40:02] 最终进度同步队列长度: 9
[2025-08-21 17:40:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:40:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:42:02] === 进度同步脚本开始执行 ===
[2025-08-21 17:42:02] === 进度同步脚本开始执行 ===
[2025-08-21 17:42:02] ✅ Redis连接成功
[2025-08-21 17:42:02] ✅ Redis连接成功
[2025-08-21 17:42:02] 当前进度同步队列长度: 0
[2025-08-21 17:42:02] 当前进度同步队列长度: 0
[2025-08-21 17:42:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于85分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0% (创建于85分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于85分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于85分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于91分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于91分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于139分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于139分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于787分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于971分钟前)
[2025-08-21 17:42:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于971分钟前)
[2025-08-21 17:42:02] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:42:02] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:42:02] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:42:02] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:42:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:42:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:42:02] 最终进度同步队列长度: 9
[2025-08-21 17:42:02] 最终进度同步队列长度: 9
[2025-08-21 17:42:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:42:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:44:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:44:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:44:01] ✅ Redis连接成功
[2025-08-21 17:44:01] ✅ Redis连接成功
[2025-08-21 17:44:01] 当前进度同步队列长度: 0
[2025-08-21 17:44:01] 当前进度同步队列长度: 0
[2025-08-21 17:44:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于87分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于87分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 92.46% (创建于87分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 92.46% (创建于87分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于93分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于93分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于141分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于141分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于789分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于973分钟前)
[2025-08-21 17:44:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于973分钟前)
[2025-08-21 17:44:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:44:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:44:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:44:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:44:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:44:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:44:01] 最终进度同步队列长度: 9
[2025-08-21 17:44:01] 最终进度同步队列长度: 9
[2025-08-21 17:44:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:44:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:46:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:46:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:46:01] ✅ Redis连接成功
[2025-08-21 17:46:01] ✅ Redis连接成功
[2025-08-21 17:46:01] 当前进度同步队列长度: 3
[2025-08-21 17:46:01] 当前进度同步队列长度: 3
[2025-08-21 17:46:01] 跳过: 订单52已在进度同步队列中
[2025-08-21 17:46:01] 跳过: 订单52已在进度同步队列中
[2025-08-21 17:46:01] 跳过: 订单51已在进度同步队列中
[2025-08-21 17:46:01] 跳过: 订单51已在进度同步队列中
[2025-08-21 17:46:01] 跳过: 订单50已在进度同步队列中
[2025-08-21 17:46:01] 跳过: 订单50已在进度同步队列中
[2025-08-21 17:46:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于143分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于143分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于791分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于975分钟前)
[2025-08-21 17:46:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于975分钟前)
[2025-08-21 17:46:01] 扫描到9个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:46:01] 扫描到9个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:46:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:46:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:46:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:46:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:46:02] 最终进度同步队列长度: 9
[2025-08-21 17:46:02] 最终进度同步队列长度: 9
[2025-08-21 17:46:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:46:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:48:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:48:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:48:01] ✅ Redis连接成功
[2025-08-21 17:48:01] ✅ Redis连接成功
[2025-08-21 17:48:01] 当前进度同步队列长度: 0
[2025-08-21 17:48:01] 当前进度同步队列长度: 0
[2025-08-21 17:48:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于91分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于91分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0.31% (创建于91分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 0.31% (创建于91分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于97分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于97分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于145分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于145分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于793分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于977分钟前)
[2025-08-21 17:48:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于977分钟前)
[2025-08-21 17:48:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:48:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:48:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:48:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:48:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:48:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:48:01] 最终进度同步队列长度: 9
[2025-08-21 17:48:01] 最终进度同步队列长度: 9
[2025-08-21 17:48:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:48:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:50:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:50:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:50:01] ✅ Redis连接成功
[2025-08-21 17:50:01] ✅ Redis连接成功
[2025-08-21 17:50:01] 当前进度同步队列长度: 3
[2025-08-21 17:50:01] 当前进度同步队列长度: 3
[2025-08-21 17:50:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于147分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于147分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于795分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于979分钟前)
[2025-08-21 17:50:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于979分钟前)
[2025-08-21 17:50:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:50:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:50:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:50:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:50:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:50:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:50:01] 最终进度同步队列长度: 9
[2025-08-21 17:50:01] 最终进度同步队列长度: 9
[2025-08-21 17:50:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:50:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:52:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:52:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:52:01] ✅ Redis连接成功
[2025-08-21 17:52:01] ✅ Redis连接成功
[2025-08-21 17:52:01] 当前进度同步队列长度: 0
[2025-08-21 17:52:01] 当前进度同步队列长度: 0
[2025-08-21 17:52:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于95分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单52 - 622429198901150742 - 队列中 - 0% (创建于95分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 10.02% (创建于95分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 10.02% (创建于95分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于101分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于101分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于149分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于149分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于797分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于981分钟前)
[2025-08-21 17:52:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于981分钟前)
[2025-08-21 17:52:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:52:01] 扫描到9个需要进度同步的订单，其中9个需要入队
[2025-08-21 17:52:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:52:01] ✅ 成功将9个订单加入进度同步队列
[2025-08-21 17:52:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:52:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:52:02] 最终进度同步队列长度: 11
[2025-08-21 17:52:02] 最终进度同步队列长度: 11
[2025-08-21 17:52:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:52:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:54:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:54:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:54:01] ✅ Redis连接成功
[2025-08-21 17:54:01] ✅ Redis连接成功
[2025-08-21 17:54:01] 当前进度同步队列长度: 2
[2025-08-21 17:54:01] 当前进度同步队列长度: 2
[2025-08-21 17:54:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于151分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于151分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于799分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于983分钟前)
[2025-08-21 17:54:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于983分钟前)
[2025-08-21 17:54:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:54:01] 扫描到6个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:54:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:54:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:54:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:54:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:54:01] 最终进度同步队列长度: 8
[2025-08-21 17:54:01] 最终进度同步队列长度: 8
[2025-08-21 17:54:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:54:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:56:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:56:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:56:01] ✅ Redis连接成功
[2025-08-21 17:56:01] ✅ Redis连接成功
[2025-08-21 17:56:01] 当前进度同步队列长度: 0
[2025-08-21 17:56:01] 当前进度同步队列长度: 0
[2025-08-21 17:56:01] 跳过: 订单51已在进度同步队列中
[2025-08-21 17:56:01] 跳过: 订单51已在进度同步队列中
[2025-08-21 17:56:01] 跳过: 订单50已在进度同步队列中
[2025-08-21 17:56:01] 跳过: 订单50已在进度同步队列中
[2025-08-21 17:56:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于153分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于153分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于801分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于985分钟前)
[2025-08-21 17:56:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于985分钟前)
[2025-08-21 17:56:01] 扫描到8个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:56:01] 扫描到8个需要进度同步的订单，其中6个需要入队
[2025-08-21 17:56:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:56:01] ✅ 成功将6个订单加入进度同步队列
[2025-08-21 17:56:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:56:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:56:01] 最终进度同步队列长度: 8
[2025-08-21 17:56:01] 最终进度同步队列长度: 8
[2025-08-21 17:56:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:56:01] === 进度同步脚本执行完成 ===
[2025-08-21 17:58:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:58:01] === 进度同步脚本开始执行 ===
[2025-08-21 17:58:01] ✅ Redis连接成功
[2025-08-21 17:58:01] ✅ Redis连接成功
[2025-08-21 17:58:01] 当前进度同步队列长度: 0
[2025-08-21 17:58:01] 当前进度同步队列长度: 0
[2025-08-21 17:58:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 25.98% (创建于101分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 25.98% (创建于101分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于107分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于107分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于155分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于155分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于803分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于987分钟前)
[2025-08-21 17:58:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于987分钟前)
[2025-08-21 17:58:01] 扫描到8个需要进度同步的订单，其中8个需要入队
[2025-08-21 17:58:01] 扫描到8个需要进度同步的订单，其中8个需要入队
[2025-08-21 17:58:01] ✅ 成功将8个订单加入进度同步队列
[2025-08-21 17:58:01] ✅ 成功将8个订单加入进度同步队列
[2025-08-21 17:58:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:58:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 17:58:02] 最终进度同步队列长度: 8
[2025-08-21 17:58:02] 最终进度同步队列长度: 8
[2025-08-21 17:58:02] === 进度同步脚本执行完成 ===
[2025-08-21 17:58:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:00:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:00:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:00:01] ✅ Redis连接成功
[2025-08-21 18:00:01] ✅ Redis连接成功
[2025-08-21 18:00:01] 当前进度同步队列长度: 0
[2025-08-21 18:00:01] 当前进度同步队列长度: 0
[2025-08-21 18:00:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 29.91% (创建于103分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 29.91% (创建于103分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于109分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于109分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于157分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于157分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于805分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于989分钟前)
[2025-08-21 18:00:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于989分钟前)
[2025-08-21 18:00:01] 扫描到8个需要进度同步的订单，其中8个需要入队
[2025-08-21 18:00:01] 扫描到8个需要进度同步的订单，其中8个需要入队
[2025-08-21 18:00:01] ✅ 成功将8个订单加入进度同步队列
[2025-08-21 18:00:01] ✅ 成功将8个订单加入进度同步队列
[2025-08-21 18:00:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:00:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:00:01] 最终进度同步队列长度: 8
[2025-08-21 18:00:01] 最终进度同步队列长度: 8
[2025-08-21 18:00:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:00:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:02:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:02:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:02:01] ✅ Redis连接成功
[2025-08-21 18:02:01] ✅ Redis连接成功
[2025-08-21 18:02:01] 当前进度同步队列长度: 0
[2025-08-21 18:02:01] 当前进度同步队列长度: 0
[2025-08-21 18:02:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 35.03% (创建于105分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 35.03% (创建于105分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于111分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于111分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于159分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于159分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单44 - 18723589157 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于807分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于991分钟前)
[2025-08-21 18:02:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于991分钟前)
[2025-08-21 18:02:01] 扫描到8个需要进度同步的订单，其中8个需要入队
[2025-08-21 18:02:01] 扫描到8个需要进度同步的订单，其中8个需要入队
[2025-08-21 18:02:01] ✅ 成功将8个订单加入进度同步队列
[2025-08-21 18:02:01] ✅ 成功将8个订单加入进度同步队列
[2025-08-21 18:02:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:02:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:02:01] 最终进度同步队列长度: 8
[2025-08-21 18:02:01] 最终进度同步队列长度: 8
[2025-08-21 18:02:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:02:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:04:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:04:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:04:02] ✅ Redis连接成功
[2025-08-21 18:04:02] ✅ Redis连接成功
[2025-08-21 18:04:02] 当前进度同步队列长度: 2
[2025-08-21 18:04:02] 当前进度同步队列长度: 2
[2025-08-21 18:04:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于161分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于161分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于809分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单45 - 15171068131 - 运行中 - 20% (创建于809分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于809分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单46 - 13957632570 - 运行中 - 20% (创建于809分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于809分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单47 - 13656580928 - 运行中 - 20% (创建于809分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于993分钟前)
[2025-08-21 18:04:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于993分钟前)
[2025-08-21 18:04:02] 扫描到5个需要进度同步的订单，其中5个需要入队
[2025-08-21 18:04:02] 扫描到5个需要进度同步的订单，其中5个需要入队
[2025-08-21 18:04:02] ✅ 成功将5个订单加入进度同步队列
[2025-08-21 18:04:02] ✅ 成功将5个订单加入进度同步队列
[2025-08-21 18:04:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:04:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:04:02] 最终进度同步队列长度: 7
[2025-08-21 18:04:02] 最终进度同步队列长度: 7
[2025-08-21 18:04:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:04:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:06:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:06:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:06:01] ✅ Redis连接成功
[2025-08-21 18:06:01] ✅ Redis连接成功
[2025-08-21 18:06:01] 当前进度同步队列长度: 0
[2025-08-21 18:06:01] 当前进度同步队列长度: 0
[2025-08-21 18:06:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 46.09% (创建于109分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 46.09% (创建于109分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于115分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于115分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于163分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于163分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于995分钟前)
[2025-08-21 18:06:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于995分钟前)
[2025-08-21 18:06:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:06:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:06:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:06:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:06:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:06:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:06:01] 最终进度同步队列长度: 4
[2025-08-21 18:06:01] 最终进度同步队列长度: 4
[2025-08-21 18:06:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:06:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:08:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:08:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:08:01] ✅ Redis连接成功
[2025-08-21 18:08:01] ✅ Redis连接成功
[2025-08-21 18:08:01] 当前进度同步队列长度: 0
[2025-08-21 18:08:01] 当前进度同步队列长度: 0
[2025-08-21 18:08:01] 跳过: 订单51已在进度同步队列中
[2025-08-21 18:08:01] 跳过: 订单51已在进度同步队列中
[2025-08-21 18:08:01] 跳过: 订单50已在进度同步队列中
[2025-08-21 18:08:01] 跳过: 订单50已在进度同步队列中
[2025-08-21 18:08:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于165分钟前)
[2025-08-21 18:08:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于165分钟前)
[2025-08-21 18:08:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于997分钟前)
[2025-08-21 18:08:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于997分钟前)
[2025-08-21 18:08:01] 扫描到4个需要进度同步的订单，其中2个需要入队
[2025-08-21 18:08:01] 扫描到4个需要进度同步的订单，其中2个需要入队
[2025-08-21 18:08:01] ✅ 成功将2个订单加入进度同步队列
[2025-08-21 18:08:01] ✅ 成功将2个订单加入进度同步队列
[2025-08-21 18:08:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:08:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:08:01] 最终进度同步队列长度: 4
[2025-08-21 18:08:01] 最终进度同步队列长度: 4
[2025-08-21 18:08:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:08:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:10:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:10:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:10:02] ✅ Redis连接成功
[2025-08-21 18:10:02] ✅ Redis连接成功
[2025-08-21 18:10:02] 当前进度同步队列长度: 0
[2025-08-21 18:10:02] 当前进度同步队列长度: 0
[2025-08-21 18:10:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 56.5% (创建于113分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 56.5% (创建于113分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于119分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于119分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于167分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于167分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于999分钟前)
[2025-08-21 18:10:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于999分钟前)
[2025-08-21 18:10:02] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:10:02] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:10:02] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:10:02] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:10:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:10:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:10:02] 最终进度同步队列长度: 4
[2025-08-21 18:10:02] 最终进度同步队列长度: 4
[2025-08-21 18:10:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:10:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:12:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:12:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:12:01] ✅ Redis连接成功
[2025-08-21 18:12:01] ✅ Redis连接成功
[2025-08-21 18:12:01] 当前进度同步队列长度: 1
[2025-08-21 18:12:01] 当前进度同步队列长度: 1
[2025-08-21 18:12:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于121分钟前)
[2025-08-21 18:12:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于121分钟前)
[2025-08-21 18:12:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于169分钟前)
[2025-08-21 18:12:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于169分钟前)
[2025-08-21 18:12:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1001分钟前)
[2025-08-21 18:12:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1001分钟前)
[2025-08-21 18:12:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:12:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:12:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:12:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:12:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:12:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:12:02] 最终进度同步队列长度: 4
[2025-08-21 18:12:02] 最终进度同步队列长度: 4
[2025-08-21 18:12:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:12:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:14:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:14:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:14:01] ✅ Redis连接成功
[2025-08-21 18:14:01] ✅ Redis连接成功
[2025-08-21 18:14:01] 当前进度同步队列长度: 0
[2025-08-21 18:14:01] 当前进度同步队列长度: 0
[2025-08-21 18:14:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 67.04% (创建于117分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 67.04% (创建于117分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于123分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于123分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于171分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于171分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1003分钟前)
[2025-08-21 18:14:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1003分钟前)
[2025-08-21 18:14:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:14:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:14:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:14:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:14:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:14:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:14:02] 最终进度同步队列长度: 4
[2025-08-21 18:14:02] 最终进度同步队列长度: 4
[2025-08-21 18:14:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:14:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:16:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:16:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:16:01] ✅ Redis连接成功
[2025-08-21 18:16:01] ✅ Redis连接成功
[2025-08-21 18:16:01] 当前进度同步队列长度: 0
[2025-08-21 18:16:01] 当前进度同步队列长度: 0
[2025-08-21 18:16:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 72.56% (创建于119分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 72.56% (创建于119分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于125分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于125分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于173分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于173分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1005分钟前)
[2025-08-21 18:16:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1005分钟前)
[2025-08-21 18:16:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:16:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:16:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:16:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:16:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:16:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:16:01] 最终进度同步队列长度: 4
[2025-08-21 18:16:01] 最终进度同步队列长度: 4
[2025-08-21 18:16:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:16:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:18:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:18:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:18:01] ✅ Redis连接成功
[2025-08-21 18:18:01] ✅ Redis连接成功
[2025-08-21 18:18:01] 当前进度同步队列长度: 0
[2025-08-21 18:18:01] 当前进度同步队列长度: 0
[2025-08-21 18:18:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 76.98% (创建于121分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 76.98% (创建于121分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于127分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于127分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于175分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于175分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1007分钟前)
[2025-08-21 18:18:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1007分钟前)
[2025-08-21 18:18:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:18:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:18:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:18:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:18:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:18:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:18:01] 最终进度同步队列长度: 4
[2025-08-21 18:18:01] 最终进度同步队列长度: 4
[2025-08-21 18:18:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:18:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:20:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:20:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:20:01] ✅ Redis连接成功
[2025-08-21 18:20:01] ✅ Redis连接成功
[2025-08-21 18:20:01] 当前进度同步队列长度: 0
[2025-08-21 18:20:01] 当前进度同步队列长度: 0
[2025-08-21 18:20:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 82.58% (创建于123分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 82.58% (创建于123分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于129分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于129分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于177分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于177分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1009分钟前)
[2025-08-21 18:20:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1009分钟前)
[2025-08-21 18:20:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:20:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:20:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:20:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:20:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:20:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:20:01] 最终进度同步队列长度: 4
[2025-08-21 18:20:01] 最终进度同步队列长度: 4
[2025-08-21 18:20:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:20:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:22:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:22:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:22:01] ✅ Redis连接成功
[2025-08-21 18:22:01] ✅ Redis连接成功
[2025-08-21 18:22:01] 当前进度同步队列长度: 0
[2025-08-21 18:22:01] 当前进度同步队列长度: 0
[2025-08-21 18:22:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 86.12% (创建于125分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 86.12% (创建于125分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于131分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于131分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于179分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于179分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1011分钟前)
[2025-08-21 18:22:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1011分钟前)
[2025-08-21 18:22:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:22:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:22:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:22:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:22:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:22:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:22:01] 最终进度同步队列长度: 4
[2025-08-21 18:22:01] 最终进度同步队列长度: 4
[2025-08-21 18:22:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:22:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:24:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:24:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:24:02] ✅ Redis连接成功
[2025-08-21 18:24:02] ✅ Redis连接成功
[2025-08-21 18:24:02] 当前进度同步队列长度: 0
[2025-08-21 18:24:02] 当前进度同步队列长度: 0
[2025-08-21 18:24:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 90.98% (创建于127分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 90.98% (创建于127分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于133分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于133分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于181分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于181分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1013分钟前)
[2025-08-21 18:24:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1013分钟前)
[2025-08-21 18:24:02] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:24:02] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:24:02] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:24:02] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:24:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:24:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:24:02] 最终进度同步队列长度: 4
[2025-08-21 18:24:02] 最终进度同步队列长度: 4
[2025-08-21 18:24:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:24:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:26:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:26:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:26:01] ✅ Redis连接成功
[2025-08-21 18:26:01] ✅ Redis连接成功
[2025-08-21 18:26:01] 当前进度同步队列长度: 0
[2025-08-21 18:26:01] 当前进度同步队列长度: 0
[2025-08-21 18:26:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 98.41% (创建于129分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 98.41% (创建于129分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于135分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于135分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于183分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于183分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1015分钟前)
[2025-08-21 18:26:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1015分钟前)
[2025-08-21 18:26:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:26:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:26:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:26:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:26:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:26:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:26:02] 最终进度同步队列长度: 4
[2025-08-21 18:26:02] 最终进度同步队列长度: 4
[2025-08-21 18:26:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:26:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:28:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:28:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:28:01] ✅ Redis连接成功
[2025-08-21 18:28:01] ✅ Redis连接成功
[2025-08-21 18:28:01] 当前进度同步队列长度: 0
[2025-08-21 18:28:01] 当前进度同步队列长度: 0
[2025-08-21 18:28:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 1.54% (创建于131分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 1.54% (创建于131分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于137分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于137分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于185分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于185分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1017分钟前)
[2025-08-21 18:28:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1017分钟前)
[2025-08-21 18:28:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:28:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:28:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:28:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:28:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:28:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:28:01] 最终进度同步队列长度: 4
[2025-08-21 18:28:01] 最终进度同步队列长度: 4
[2025-08-21 18:28:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:28:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:30:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:30:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:30:01] ✅ Redis连接成功
[2025-08-21 18:30:01] ✅ Redis连接成功
[2025-08-21 18:30:01] 当前进度同步队列长度: 0
[2025-08-21 18:30:01] 当前进度同步队列长度: 0
[2025-08-21 18:30:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 3.61% (创建于133分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 3.61% (创建于133分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于139分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于139分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于187分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于187分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1019分钟前)
[2025-08-21 18:30:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1019分钟前)
[2025-08-21 18:30:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:30:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:30:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:30:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:30:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:30:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:30:01] 最终进度同步队列长度: 4
[2025-08-21 18:30:01] 最终进度同步队列长度: 4
[2025-08-21 18:30:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:30:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:32:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:32:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:32:02] ✅ Redis连接成功
[2025-08-21 18:32:02] ✅ Redis连接成功
[2025-08-21 18:32:02] 当前进度同步队列长度: 0
[2025-08-21 18:32:02] 当前进度同步队列长度: 0
[2025-08-21 18:32:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 6.51% (创建于135分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 6.51% (创建于135分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于141分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于141分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于189分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于189分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1021分钟前)
[2025-08-21 18:32:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1021分钟前)
[2025-08-21 18:32:02] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:32:02] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:32:02] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:32:02] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:32:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:32:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:32:02] 最终进度同步队列长度: 4
[2025-08-21 18:32:02] 最终进度同步队列长度: 4
[2025-08-21 18:32:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:32:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:34:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:34:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:34:01] ✅ Redis连接成功
[2025-08-21 18:34:01] ✅ Redis连接成功
[2025-08-21 18:34:01] 当前进度同步队列长度: 0
[2025-08-21 18:34:01] 当前进度同步队列长度: 0
[2025-08-21 18:34:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 9.25% (创建于137分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 9.25% (创建于137分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于143分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于143分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于191分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于191分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1023分钟前)
[2025-08-21 18:34:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1023分钟前)
[2025-08-21 18:34:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:34:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:34:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:34:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:34:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:34:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:34:01] 最终进度同步队列长度: 4
[2025-08-21 18:34:01] 最终进度同步队列长度: 4
[2025-08-21 18:34:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:34:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:36:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:36:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:36:01] ✅ Redis连接成功
[2025-08-21 18:36:01] ✅ Redis连接成功
[2025-08-21 18:36:01] 当前进度同步队列长度: 0
[2025-08-21 18:36:01] 当前进度同步队列长度: 0
[2025-08-21 18:36:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 10.65% (创建于139分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 10.65% (创建于139分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于145分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于145分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于193分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于193分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1025分钟前)
[2025-08-21 18:36:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1025分钟前)
[2025-08-21 18:36:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:36:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:36:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:36:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:36:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:36:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:36:01] 最终进度同步队列长度: 4
[2025-08-21 18:36:01] 最终进度同步队列长度: 4
[2025-08-21 18:36:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:36:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:38:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:38:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:38:01] ✅ Redis连接成功
[2025-08-21 18:38:01] ✅ Redis连接成功
[2025-08-21 18:38:01] 当前进度同步队列长度: 0
[2025-08-21 18:38:01] 当前进度同步队列长度: 0
[2025-08-21 18:38:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 15.24% (创建于141分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 15.24% (创建于141分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于147分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于147分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于195分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单48 - 13649324102 - 运行中 - 20% (创建于195分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1027分钟前)
[2025-08-21 18:38:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1027分钟前)
[2025-08-21 18:38:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:38:01] 扫描到4个需要进度同步的订单，其中4个需要入队
[2025-08-21 18:38:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:38:01] ✅ 成功将4个订单加入进度同步队列
[2025-08-21 18:38:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:38:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:38:01] 最终进度同步队列长度: 4
[2025-08-21 18:38:01] 最终进度同步队列长度: 4
[2025-08-21 18:38:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:38:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:40:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:40:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:40:01] ✅ Redis连接成功
[2025-08-21 18:40:01] ✅ Redis连接成功
[2025-08-21 18:40:01] 当前进度同步队列长度: 0
[2025-08-21 18:40:01] 当前进度同步队列长度: 0
[2025-08-21 18:40:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 17.31% (创建于143分钟前)
[2025-08-21 18:40:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 17.31% (创建于143分钟前)
[2025-08-21 18:40:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于149分钟前)
[2025-08-21 18:40:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于149分钟前)
[2025-08-21 18:40:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1029分钟前)
[2025-08-21 18:40:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1029分钟前)
[2025-08-21 18:40:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:40:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:40:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:40:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:40:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:40:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:40:01] 最终进度同步队列长度: 3
[2025-08-21 18:40:01] 最终进度同步队列长度: 3
[2025-08-21 18:40:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:40:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:42:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:42:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:42:01] ✅ Redis连接成功
[2025-08-21 18:42:01] ✅ Redis连接成功
[2025-08-21 18:42:01] 当前进度同步队列长度: 0
[2025-08-21 18:42:01] 当前进度同步队列长度: 0
[2025-08-21 18:42:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 19.8% (创建于145分钟前)
[2025-08-21 18:42:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 19.8% (创建于145分钟前)
[2025-08-21 18:42:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于151分钟前)
[2025-08-21 18:42:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于151分钟前)
[2025-08-21 18:42:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1031分钟前)
[2025-08-21 18:42:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1031分钟前)
[2025-08-21 18:42:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:42:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:42:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:42:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:42:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:42:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:42:01] 最终进度同步队列长度: 3
[2025-08-21 18:42:01] 最终进度同步队列长度: 3
[2025-08-21 18:42:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:42:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:44:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:44:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:44:01] ✅ Redis连接成功
[2025-08-21 18:44:01] ✅ Redis连接成功
[2025-08-21 18:44:01] 当前进度同步队列长度: 0
[2025-08-21 18:44:01] 当前进度同步队列长度: 0
[2025-08-21 18:44:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 23.67% (创建于147分钟前)
[2025-08-21 18:44:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 23.67% (创建于147分钟前)
[2025-08-21 18:44:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于153分钟前)
[2025-08-21 18:44:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于153分钟前)
[2025-08-21 18:44:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1033分钟前)
[2025-08-21 18:44:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1033分钟前)
[2025-08-21 18:44:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:44:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:44:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:44:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:44:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:44:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:44:01] 最终进度同步队列长度: 3
[2025-08-21 18:44:01] 最终进度同步队列长度: 3
[2025-08-21 18:44:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:44:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:46:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:46:02] === 进度同步脚本开始执行 ===
[2025-08-21 18:46:02] ✅ Redis连接成功
[2025-08-21 18:46:02] ✅ Redis连接成功
[2025-08-21 18:46:02] 当前进度同步队列长度: 0
[2025-08-21 18:46:02] 当前进度同步队列长度: 0
[2025-08-21 18:46:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 26.18% (创建于149分钟前)
[2025-08-21 18:46:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 26.18% (创建于149分钟前)
[2025-08-21 18:46:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于155分钟前)
[2025-08-21 18:46:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于155分钟前)
[2025-08-21 18:46:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1035分钟前)
[2025-08-21 18:46:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1035分钟前)
[2025-08-21 18:46:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:46:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:46:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:46:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:46:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:46:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:46:02] 最终进度同步队列长度: 3
[2025-08-21 18:46:02] 最终进度同步队列长度: 3
[2025-08-21 18:46:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:46:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:48:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:48:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:48:01] ✅ Redis连接成功
[2025-08-21 18:48:01] ✅ Redis连接成功
[2025-08-21 18:48:01] 当前进度同步队列长度: 0
[2025-08-21 18:48:01] 当前进度同步队列长度: 0
[2025-08-21 18:48:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 28.94% (创建于151分钟前)
[2025-08-21 18:48:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 28.94% (创建于151分钟前)
[2025-08-21 18:48:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于157分钟前)
[2025-08-21 18:48:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于157分钟前)
[2025-08-21 18:48:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1037分钟前)
[2025-08-21 18:48:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1037分钟前)
[2025-08-21 18:48:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:48:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:48:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:48:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:48:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:48:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:48:02] 最终进度同步队列长度: 3
[2025-08-21 18:48:02] 最终进度同步队列长度: 3
[2025-08-21 18:48:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:48:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:50:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:50:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:50:01] ✅ Redis连接成功
[2025-08-21 18:50:01] ✅ Redis连接成功
[2025-08-21 18:50:01] 当前进度同步队列长度: 0
[2025-08-21 18:50:01] 当前进度同步队列长度: 0
[2025-08-21 18:50:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 31.43% (创建于153分钟前)
[2025-08-21 18:50:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 31.43% (创建于153分钟前)
[2025-08-21 18:50:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于159分钟前)
[2025-08-21 18:50:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于159分钟前)
[2025-08-21 18:50:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1039分钟前)
[2025-08-21 18:50:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1039分钟前)
[2025-08-21 18:50:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:50:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:50:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:50:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:50:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:50:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:50:01] 最终进度同步队列长度: 3
[2025-08-21 18:50:01] 最终进度同步队列长度: 3
[2025-08-21 18:50:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:50:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:52:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:52:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:52:01] ✅ Redis连接成功
[2025-08-21 18:52:01] ✅ Redis连接成功
[2025-08-21 18:52:01] 当前进度同步队列长度: 0
[2025-08-21 18:52:01] 当前进度同步队列长度: 0
[2025-08-21 18:52:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 33.68% (创建于155分钟前)
[2025-08-21 18:52:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 33.68% (创建于155分钟前)
[2025-08-21 18:52:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于161分钟前)
[2025-08-21 18:52:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于161分钟前)
[2025-08-21 18:52:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1041分钟前)
[2025-08-21 18:52:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1041分钟前)
[2025-08-21 18:52:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:52:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:52:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:52:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:52:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:52:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:52:01] 最终进度同步队列长度: 3
[2025-08-21 18:52:01] 最终进度同步队列长度: 3
[2025-08-21 18:52:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:52:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:54:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:54:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:54:01] ✅ Redis连接成功
[2025-08-21 18:54:01] ✅ Redis连接成功
[2025-08-21 18:54:01] 当前进度同步队列长度: 0
[2025-08-21 18:54:01] 当前进度同步队列长度: 0
[2025-08-21 18:54:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 36.74% (创建于157分钟前)
[2025-08-21 18:54:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 36.74% (创建于157分钟前)
[2025-08-21 18:54:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于163分钟前)
[2025-08-21 18:54:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于163分钟前)
[2025-08-21 18:54:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1043分钟前)
[2025-08-21 18:54:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1043分钟前)
[2025-08-21 18:54:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:54:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:54:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:54:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:54:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:54:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:54:02] 最终进度同步队列长度: 3
[2025-08-21 18:54:02] 最终进度同步队列长度: 3
[2025-08-21 18:54:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:54:02] === 进度同步脚本执行完成 ===
[2025-08-21 18:56:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:56:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:56:01] ✅ Redis连接成功
[2025-08-21 18:56:01] ✅ Redis连接成功
[2025-08-21 18:56:01] 当前进度同步队列长度: 0
[2025-08-21 18:56:01] 当前进度同步队列长度: 0
[2025-08-21 18:56:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 39.39% (创建于159分钟前)
[2025-08-21 18:56:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 39.39% (创建于159分钟前)
[2025-08-21 18:56:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于165分钟前)
[2025-08-21 18:56:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于165分钟前)
[2025-08-21 18:56:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1045分钟前)
[2025-08-21 18:56:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1045分钟前)
[2025-08-21 18:56:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:56:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:56:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:56:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:56:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:56:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:56:01] 最终进度同步队列长度: 3
[2025-08-21 18:56:01] 最终进度同步队列长度: 3
[2025-08-21 18:56:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:56:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:58:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:58:01] === 进度同步脚本开始执行 ===
[2025-08-21 18:58:01] ✅ Redis连接成功
[2025-08-21 18:58:01] ✅ Redis连接成功
[2025-08-21 18:58:01] 当前进度同步队列长度: 0
[2025-08-21 18:58:01] 当前进度同步队列长度: 0
[2025-08-21 18:58:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 42.37% (创建于161分钟前)
[2025-08-21 18:58:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 42.37% (创建于161分钟前)
[2025-08-21 18:58:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于167分钟前)
[2025-08-21 18:58:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于167分钟前)
[2025-08-21 18:58:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1047分钟前)
[2025-08-21 18:58:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1047分钟前)
[2025-08-21 18:58:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:58:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 18:58:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:58:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 18:58:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:58:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 18:58:01] 最终进度同步队列长度: 3
[2025-08-21 18:58:01] 最终进度同步队列长度: 3
[2025-08-21 18:58:01] === 进度同步脚本执行完成 ===
[2025-08-21 18:58:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:00:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:00:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:00:01] ✅ Redis连接成功
[2025-08-21 19:00:01] ✅ Redis连接成功
[2025-08-21 19:00:01] 当前进度同步队列长度: 0
[2025-08-21 19:00:01] 当前进度同步队列长度: 0
[2025-08-21 19:00:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 45.13% (创建于163分钟前)
[2025-08-21 19:00:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 45.13% (创建于163分钟前)
[2025-08-21 19:00:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于169分钟前)
[2025-08-21 19:00:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于169分钟前)
[2025-08-21 19:00:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1049分钟前)
[2025-08-21 19:00:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1049分钟前)
[2025-08-21 19:00:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:00:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:00:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:00:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:00:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:00:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:00:02] 最终进度同步队列长度: 3
[2025-08-21 19:00:02] 最终进度同步队列长度: 3
[2025-08-21 19:00:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:00:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:02:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:02:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:02:01] ✅ Redis连接成功
[2025-08-21 19:02:01] ✅ Redis连接成功
[2025-08-21 19:02:01] 当前进度同步队列长度: 0
[2025-08-21 19:02:01] 当前进度同步队列长度: 0
[2025-08-21 19:02:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 47.31% (创建于165分钟前)
[2025-08-21 19:02:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 47.31% (创建于165分钟前)
[2025-08-21 19:02:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于171分钟前)
[2025-08-21 19:02:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于171分钟前)
[2025-08-21 19:02:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1051分钟前)
[2025-08-21 19:02:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1051分钟前)
[2025-08-21 19:02:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:02:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:02:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:02:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:02:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:02:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:02:02] 最终进度同步队列长度: 3
[2025-08-21 19:02:02] 最终进度同步队列长度: 3
[2025-08-21 19:02:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:02:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:04:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:04:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:04:01] ✅ Redis连接成功
[2025-08-21 19:04:01] ✅ Redis连接成功
[2025-08-21 19:04:01] 当前进度同步队列长度: 0
[2025-08-21 19:04:01] 当前进度同步队列长度: 0
[2025-08-21 19:04:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 50.81% (创建于167分钟前)
[2025-08-21 19:04:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 50.81% (创建于167分钟前)
[2025-08-21 19:04:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于173分钟前)
[2025-08-21 19:04:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于173分钟前)
[2025-08-21 19:04:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1053分钟前)
[2025-08-21 19:04:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1053分钟前)
[2025-08-21 19:04:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:04:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:04:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:04:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:04:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:04:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:04:01] 最终进度同步队列长度: 3
[2025-08-21 19:04:01] 最终进度同步队列长度: 3
[2025-08-21 19:04:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:04:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:06:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:06:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:06:01] ✅ Redis连接成功
[2025-08-21 19:06:01] ✅ Redis连接成功
[2025-08-21 19:06:01] 当前进度同步队列长度: 0
[2025-08-21 19:06:01] 当前进度同步队列长度: 0
[2025-08-21 19:06:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 53.06% (创建于169分钟前)
[2025-08-21 19:06:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 53.06% (创建于169分钟前)
[2025-08-21 19:06:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于175分钟前)
[2025-08-21 19:06:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于175分钟前)
[2025-08-21 19:06:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1055分钟前)
[2025-08-21 19:06:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1055分钟前)
[2025-08-21 19:06:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:06:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:06:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:06:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:06:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:06:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:06:01] 最终进度同步队列长度: 3
[2025-08-21 19:06:01] 最终进度同步队列长度: 3
[2025-08-21 19:06:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:06:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:08:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:08:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:08:02] ✅ Redis连接成功
[2025-08-21 19:08:02] ✅ Redis连接成功
[2025-08-21 19:08:02] 当前进度同步队列长度: 0
[2025-08-21 19:08:02] 当前进度同步队列长度: 0
[2025-08-21 19:08:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 55.45% (创建于171分钟前)
[2025-08-21 19:08:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 55.45% (创建于171分钟前)
[2025-08-21 19:08:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于177分钟前)
[2025-08-21 19:08:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于177分钟前)
[2025-08-21 19:08:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1057分钟前)
[2025-08-21 19:08:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1057分钟前)
[2025-08-21 19:08:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:08:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:08:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:08:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:08:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:08:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:08:02] 最终进度同步队列长度: 3
[2025-08-21 19:08:02] 最终进度同步队列长度: 3
[2025-08-21 19:08:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:08:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:10:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:10:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:10:01] ✅ Redis连接成功
[2025-08-21 19:10:01] ✅ Redis连接成功
[2025-08-21 19:10:01] 当前进度同步队列长度: 0
[2025-08-21 19:10:01] 当前进度同步队列长度: 0
[2025-08-21 19:10:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 58.25% (创建于173分钟前)
[2025-08-21 19:10:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 58.25% (创建于173分钟前)
[2025-08-21 19:10:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于179分钟前)
[2025-08-21 19:10:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于179分钟前)
[2025-08-21 19:10:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1059分钟前)
[2025-08-21 19:10:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1059分钟前)
[2025-08-21 19:10:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:10:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:10:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:10:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:10:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:10:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:10:02] 最终进度同步队列长度: 3
[2025-08-21 19:10:02] 最终进度同步队列长度: 3
[2025-08-21 19:10:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:10:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:12:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:12:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:12:01] ✅ Redis连接成功
[2025-08-21 19:12:01] ✅ Redis连接成功
[2025-08-21 19:12:01] 当前进度同步队列长度: 0
[2025-08-21 19:12:01] 当前进度同步队列长度: 0
[2025-08-21 19:12:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 61.19% (创建于175分钟前)
[2025-08-21 19:12:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 61.19% (创建于175分钟前)
[2025-08-21 19:12:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于181分钟前)
[2025-08-21 19:12:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于181分钟前)
[2025-08-21 19:12:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1061分钟前)
[2025-08-21 19:12:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1061分钟前)
[2025-08-21 19:12:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:12:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:12:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:12:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:12:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:12:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:12:01] 最终进度同步队列长度: 0
[2025-08-21 19:12:01] 最终进度同步队列长度: 0
[2025-08-21 19:12:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:12:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:14:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:14:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:14:01] ✅ Redis连接成功
[2025-08-21 19:14:01] ✅ Redis连接成功
[2025-08-21 19:14:01] 当前进度同步队列长度: 0
[2025-08-21 19:14:01] 当前进度同步队列长度: 0
[2025-08-21 19:14:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 63.77% (创建于177分钟前)
[2025-08-21 19:14:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 63.77% (创建于177分钟前)
[2025-08-21 19:14:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于183分钟前)
[2025-08-21 19:14:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于183分钟前)
[2025-08-21 19:14:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1063分钟前)
[2025-08-21 19:14:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1063分钟前)
[2025-08-21 19:14:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:14:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:14:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:14:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:14:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:14:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:14:01] 最终进度同步队列长度: 3
[2025-08-21 19:14:01] 最终进度同步队列长度: 3
[2025-08-21 19:14:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:14:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:16:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:16:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:16:01] ✅ Redis连接成功
[2025-08-21 19:16:01] ✅ Redis连接成功
[2025-08-21 19:16:01] 当前进度同步队列长度: 0
[2025-08-21 19:16:01] 当前进度同步队列长度: 0
[2025-08-21 19:16:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 66.65% (创建于179分钟前)
[2025-08-21 19:16:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 66.65% (创建于179分钟前)
[2025-08-21 19:16:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于185分钟前)
[2025-08-21 19:16:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于185分钟前)
[2025-08-21 19:16:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1065分钟前)
[2025-08-21 19:16:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1065分钟前)
[2025-08-21 19:16:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:16:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:16:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:16:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:16:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:16:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:16:02] 最终进度同步队列长度: 3
[2025-08-21 19:16:02] 最终进度同步队列长度: 3
[2025-08-21 19:16:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:16:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:18:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:18:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:18:01] ✅ Redis连接成功
[2025-08-21 19:18:01] ✅ Redis连接成功
[2025-08-21 19:18:01] 当前进度同步队列长度: 0
[2025-08-21 19:18:01] 当前进度同步队列长度: 0
[2025-08-21 19:18:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 69.5% (创建于181分钟前)
[2025-08-21 19:18:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 69.5% (创建于181分钟前)
[2025-08-21 19:18:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于187分钟前)
[2025-08-21 19:18:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于187分钟前)
[2025-08-21 19:18:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1067分钟前)
[2025-08-21 19:18:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1067分钟前)
[2025-08-21 19:18:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:18:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:18:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:18:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:18:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:18:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:18:01] 最终进度同步队列长度: 3
[2025-08-21 19:18:01] 最终进度同步队列长度: 3
[2025-08-21 19:18:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:18:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:20:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:20:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:20:01] ✅ Redis连接成功
[2025-08-21 19:20:01] ✅ Redis连接成功
[2025-08-21 19:20:01] 当前进度同步队列长度: 0
[2025-08-21 19:20:01] 当前进度同步队列长度: 0
[2025-08-21 19:20:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 71.57% (创建于183分钟前)
[2025-08-21 19:20:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 71.57% (创建于183分钟前)
[2025-08-21 19:20:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于189分钟前)
[2025-08-21 19:20:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于189分钟前)
[2025-08-21 19:20:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1069分钟前)
[2025-08-21 19:20:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1069分钟前)
[2025-08-21 19:20:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:20:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:20:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:20:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:20:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:20:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:20:01] 最终进度同步队列长度: 3
[2025-08-21 19:20:01] 最终进度同步队列长度: 3
[2025-08-21 19:20:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:20:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:22:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:22:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:22:02] ✅ Redis连接成功
[2025-08-21 19:22:02] ✅ Redis连接成功
[2025-08-21 19:22:02] 当前进度同步队列长度: 0
[2025-08-21 19:22:02] 当前进度同步队列长度: 0
[2025-08-21 19:22:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 74.35% (创建于185分钟前)
[2025-08-21 19:22:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 74.35% (创建于185分钟前)
[2025-08-21 19:22:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于191分钟前)
[2025-08-21 19:22:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于191分钟前)
[2025-08-21 19:22:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1071分钟前)
[2025-08-21 19:22:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1071分钟前)
[2025-08-21 19:22:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:22:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:22:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:22:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:22:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:22:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:22:02] 最终进度同步队列长度: 3
[2025-08-21 19:22:02] 最终进度同步队列长度: 3
[2025-08-21 19:22:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:22:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:24:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:24:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:24:01] ✅ Redis连接成功
[2025-08-21 19:24:01] ✅ Redis连接成功
[2025-08-21 19:24:01] 当前进度同步队列长度: 0
[2025-08-21 19:24:01] 当前进度同步队列长度: 0
[2025-08-21 19:24:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 77.49% (创建于187分钟前)
[2025-08-21 19:24:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 77.49% (创建于187分钟前)
[2025-08-21 19:24:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于193分钟前)
[2025-08-21 19:24:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于193分钟前)
[2025-08-21 19:24:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1073分钟前)
[2025-08-21 19:24:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1073分钟前)
[2025-08-21 19:24:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:24:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:24:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:24:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:24:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:24:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:24:02] 最终进度同步队列长度: 3
[2025-08-21 19:24:02] 最终进度同步队列长度: 3
[2025-08-21 19:24:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:24:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:26:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:26:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:26:01] ✅ Redis连接成功
[2025-08-21 19:26:01] ✅ Redis连接成功
[2025-08-21 19:26:01] 当前进度同步队列长度: 0
[2025-08-21 19:26:01] 当前进度同步队列长度: 0
[2025-08-21 19:26:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 79.93% (创建于189分钟前)
[2025-08-21 19:26:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 79.93% (创建于189分钟前)
[2025-08-21 19:26:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于195分钟前)
[2025-08-21 19:26:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于195分钟前)
[2025-08-21 19:26:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1075分钟前)
[2025-08-21 19:26:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1075分钟前)
[2025-08-21 19:26:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:26:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:26:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:26:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:26:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:26:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:26:01] 最终进度同步队列长度: 3
[2025-08-21 19:26:01] 最终进度同步队列长度: 3
[2025-08-21 19:26:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:26:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:28:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:28:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:28:01] ✅ Redis连接成功
[2025-08-21 19:28:01] ✅ Redis连接成功
[2025-08-21 19:28:01] 当前进度同步队列长度: 0
[2025-08-21 19:28:01] 当前进度同步队列长度: 0
[2025-08-21 19:28:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 82.92% (创建于191分钟前)
[2025-08-21 19:28:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 82.92% (创建于191分钟前)
[2025-08-21 19:28:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于197分钟前)
[2025-08-21 19:28:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于197分钟前)
[2025-08-21 19:28:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1077分钟前)
[2025-08-21 19:28:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1077分钟前)
[2025-08-21 19:28:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:28:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:28:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:28:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:28:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:28:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:28:01] 最终进度同步队列长度: 3
[2025-08-21 19:28:01] 最终进度同步队列长度: 3
[2025-08-21 19:28:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:28:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:30:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:30:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:30:02] ✅ Redis连接成功
[2025-08-21 19:30:02] ✅ Redis连接成功
[2025-08-21 19:30:02] 当前进度同步队列长度: 0
[2025-08-21 19:30:02] 当前进度同步队列长度: 0
[2025-08-21 19:30:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 85.73% (创建于193分钟前)
[2025-08-21 19:30:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 85.73% (创建于193分钟前)
[2025-08-21 19:30:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于199分钟前)
[2025-08-21 19:30:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于199分钟前)
[2025-08-21 19:30:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1079分钟前)
[2025-08-21 19:30:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1079分钟前)
[2025-08-21 19:30:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:30:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:30:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:30:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:30:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:30:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:30:02] 最终进度同步队列长度: 3
[2025-08-21 19:30:02] 最终进度同步队列长度: 3
[2025-08-21 19:30:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:30:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:32:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:32:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:32:01] ✅ Redis连接成功
[2025-08-21 19:32:01] ✅ Redis连接成功
[2025-08-21 19:32:01] 当前进度同步队列长度: 0
[2025-08-21 19:32:01] 当前进度同步队列长度: 0
[2025-08-21 19:32:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 88.03% (创建于195分钟前)
[2025-08-21 19:32:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 88.03% (创建于195分钟前)
[2025-08-21 19:32:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于201分钟前)
[2025-08-21 19:32:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于201分钟前)
[2025-08-21 19:32:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1081分钟前)
[2025-08-21 19:32:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1081分钟前)
[2025-08-21 19:32:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:32:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:32:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:32:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:32:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:32:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:32:02] 最终进度同步队列长度: 3
[2025-08-21 19:32:02] 最终进度同步队列长度: 3
[2025-08-21 19:32:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:32:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:34:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:34:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:34:01] ✅ Redis连接成功
[2025-08-21 19:34:01] ✅ Redis连接成功
[2025-08-21 19:34:01] 当前进度同步队列长度: 0
[2025-08-21 19:34:01] 当前进度同步队列长度: 0
[2025-08-21 19:34:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 91.22% (创建于197分钟前)
[2025-08-21 19:34:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 91.22% (创建于197分钟前)
[2025-08-21 19:34:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于203分钟前)
[2025-08-21 19:34:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于203分钟前)
[2025-08-21 19:34:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1083分钟前)
[2025-08-21 19:34:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1083分钟前)
[2025-08-21 19:34:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:34:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:34:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:34:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:34:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:34:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:34:01] 最终进度同步队列长度: 3
[2025-08-21 19:34:01] 最终进度同步队列长度: 3
[2025-08-21 19:34:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:34:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:36:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:36:02] === 进度同步脚本开始执行 ===
[2025-08-21 19:36:02] ✅ Redis连接成功
[2025-08-21 19:36:02] ✅ Redis连接成功
[2025-08-21 19:36:02] 当前进度同步队列长度: 0
[2025-08-21 19:36:02] 当前进度同步队列长度: 0
[2025-08-21 19:36:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 93.91% (创建于199分钟前)
[2025-08-21 19:36:02] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 93.91% (创建于199分钟前)
[2025-08-21 19:36:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于205分钟前)
[2025-08-21 19:36:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于205分钟前)
[2025-08-21 19:36:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1085分钟前)
[2025-08-21 19:36:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1085分钟前)
[2025-08-21 19:36:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:36:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:36:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:36:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:36:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:36:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:36:02] 最终进度同步队列长度: 3
[2025-08-21 19:36:02] 最终进度同步队列长度: 3
[2025-08-21 19:36:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:36:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:38:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:38:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:38:01] ✅ Redis连接成功
[2025-08-21 19:38:01] ✅ Redis连接成功
[2025-08-21 19:38:01] 当前进度同步队列长度: 0
[2025-08-21 19:38:01] 当前进度同步队列长度: 0
[2025-08-21 19:38:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 96.83% (创建于201分钟前)
[2025-08-21 19:38:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 96.83% (创建于201分钟前)
[2025-08-21 19:38:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于207分钟前)
[2025-08-21 19:38:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于207分钟前)
[2025-08-21 19:38:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1087分钟前)
[2025-08-21 19:38:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1087分钟前)
[2025-08-21 19:38:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:38:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:38:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:38:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:38:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:38:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:38:01] 最终进度同步队列长度: 3
[2025-08-21 19:38:01] 最终进度同步队列长度: 3
[2025-08-21 19:38:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:38:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:40:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:40:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:40:01] ✅ Redis连接成功
[2025-08-21 19:40:01] ✅ Redis连接成功
[2025-08-21 19:40:01] 当前进度同步队列长度: 1
[2025-08-21 19:40:01] 当前进度同步队列长度: 1
[2025-08-21 19:40:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于209分钟前)
[2025-08-21 19:40:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于209分钟前)
[2025-08-21 19:40:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1089分钟前)
[2025-08-21 19:40:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1089分钟前)
[2025-08-21 19:40:01] 扫描到2个需要进度同步的订单，其中2个需要入队
[2025-08-21 19:40:01] 扫描到2个需要进度同步的订单，其中2个需要入队
[2025-08-21 19:40:01] ✅ 成功将2个订单加入进度同步队列
[2025-08-21 19:40:01] ✅ 成功将2个订单加入进度同步队列
[2025-08-21 19:40:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:40:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:40:01] 最终进度同步队列长度: 3
[2025-08-21 19:40:01] 最终进度同步队列长度: 3
[2025-08-21 19:40:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:40:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:42:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:42:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:42:01] ✅ Redis连接成功
[2025-08-21 19:42:01] ✅ Redis连接成功
[2025-08-21 19:42:01] 当前进度同步队列长度: 0
[2025-08-21 19:42:01] 当前进度同步队列长度: 0
[2025-08-21 19:42:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 3.32% (创建于205分钟前)
[2025-08-21 19:42:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 3.32% (创建于205分钟前)
[2025-08-21 19:42:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于211分钟前)
[2025-08-21 19:42:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于211分钟前)
[2025-08-21 19:42:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1091分钟前)
[2025-08-21 19:42:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1091分钟前)
[2025-08-21 19:42:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:42:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:42:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:42:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:42:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:42:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:42:01] 最终进度同步队列长度: 3
[2025-08-21 19:42:01] 最终进度同步队列长度: 3
[2025-08-21 19:42:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:42:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:44:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:44:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:44:01] ✅ Redis连接成功
[2025-08-21 19:44:01] ✅ Redis连接成功
[2025-08-21 19:44:01] 当前进度同步队列长度: 0
[2025-08-21 19:44:01] 当前进度同步队列长度: 0
[2025-08-21 19:44:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 8.81% (创建于207分钟前)
[2025-08-21 19:44:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 8.81% (创建于207分钟前)
[2025-08-21 19:44:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于213分钟前)
[2025-08-21 19:44:02] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于213分钟前)
[2025-08-21 19:44:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1093分钟前)
[2025-08-21 19:44:02] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1093分钟前)
[2025-08-21 19:44:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:44:02] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:44:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:44:02] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:44:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:44:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:44:02] 最终进度同步队列长度: 3
[2025-08-21 19:44:02] 最终进度同步队列长度: 3
[2025-08-21 19:44:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:44:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:46:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:46:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:46:01] ✅ Redis连接成功
[2025-08-21 19:46:01] ✅ Redis连接成功
[2025-08-21 19:46:01] 当前进度同步队列长度: 0
[2025-08-21 19:46:01] 当前进度同步队列长度: 0
[2025-08-21 19:46:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 13.65% (创建于209分钟前)
[2025-08-21 19:46:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 13.65% (创建于209分钟前)
[2025-08-21 19:46:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于215分钟前)
[2025-08-21 19:46:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于215分钟前)
[2025-08-21 19:46:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1095分钟前)
[2025-08-21 19:46:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1095分钟前)
[2025-08-21 19:46:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:46:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:46:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:46:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:46:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:46:02] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:46:02] 最终进度同步队列长度: 3
[2025-08-21 19:46:02] 最终进度同步队列长度: 3
[2025-08-21 19:46:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:46:02] === 进度同步脚本执行完成 ===
[2025-08-21 19:48:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:48:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:48:01] ✅ Redis连接成功
[2025-08-21 19:48:01] ✅ Redis连接成功
[2025-08-21 19:48:01] 当前进度同步队列长度: 0
[2025-08-21 19:48:01] 当前进度同步队列长度: 0
[2025-08-21 19:48:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 18.18% (创建于211分钟前)
[2025-08-21 19:48:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 18.18% (创建于211分钟前)
[2025-08-21 19:48:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于217分钟前)
[2025-08-21 19:48:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于217分钟前)
[2025-08-21 19:48:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1097分钟前)
[2025-08-21 19:48:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1097分钟前)
[2025-08-21 19:48:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:48:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:48:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:48:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:48:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:48:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:48:01] 最终进度同步队列长度: 3
[2025-08-21 19:48:01] 最终进度同步队列长度: 3
[2025-08-21 19:48:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:48:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:50:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:50:01] === 进度同步脚本开始执行 ===
[2025-08-21 19:50:01] ✅ Redis连接成功
[2025-08-21 19:50:01] ✅ Redis连接成功
[2025-08-21 19:50:01] 当前进度同步队列长度: 0
[2025-08-21 19:50:01] 当前进度同步队列长度: 0
[2025-08-21 19:50:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 23.19% (创建于213分钟前)
[2025-08-21 19:50:01] 准备进度同步: 订单51 - 622429198901150742 - 进行中 - 23.19% (创建于213分钟前)
[2025-08-21 19:50:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于219分钟前)
[2025-08-21 19:50:01] 准备进度同步: 订单50 - 622429199710014822 - 运行中 - 20% (创建于219分钟前)
[2025-08-21 19:50:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1099分钟前)
[2025-08-21 19:50:01] 准备进度同步: 订单5 - 622429199512250226 - 队列中 - 10% (创建于1099分钟前)
[2025-08-21 19:50:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:50:01] 扫描到3个需要进度同步的订单，其中3个需要入队
[2025-08-21 19:50:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:50:01] ✅ 成功将3个订单加入进度同步队列
[2025-08-21 19:50:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:50:01] 守护进程状态: 2/3 个进程在运行
[2025-08-21 19:50:01] 最终进度同步队列长度: 3
[2025-08-21 19:50:01] 最终进度同步队列长度: 3
[2025-08-21 19:50:01] === 进度同步脚本执行完成 ===
[2025-08-21 19:50:01] === 进度同步脚本执行完成 ===
