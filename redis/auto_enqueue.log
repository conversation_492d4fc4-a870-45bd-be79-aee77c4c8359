[2025-08-21 04:12:16] === 自动入队脚本开始执行 ===
[2025-08-21 04:12:16] ✅ Redis连接成功
[2025-08-21 04:12:16] 当前队列长度: 0
[2025-08-21 04:12:16] 准备入队: 订单21 - 15809461689 - 运行中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单22 - 13919690202 - 运行中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单23 - 13993257536 - 运行中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单24 - 18293274653 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单25 - 15095497017 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单26 - 15968672873 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单27 - 13957646391 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单28 - 15267284657 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单29 - 18871818687 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单30 - 15587551626 - 上号中 - 获取失败
[2025-08-21 04:12:16] 准备入队: 订单5 - 622429199512250226 - 队列中 - 获取失败
[2025-08-21 04:12:16] 扫描到11个订单，其中{count(Array)}个需要入队
[2025-08-21 04:12:16] ✅ 成功入队11个订单
[2025-08-21 04:12:16] 更新11个订单状态为'待更新'
[2025-08-21 04:12:16] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:12:16] 最终队列长度: 11
[2025-08-21 04:12:16] === 自动入队脚本执行完成 ===
[2025-08-21 04:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:18:01] ✅ Redis连接成功
[2025-08-21 04:18:01] ✅ Redis连接成功
[2025-08-21 04:18:01] 当前队列长度: 0
[2025-08-21 04:18:01] 当前队列长度: 0
[2025-08-21 04:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:18:02] 最终队列长度: 0
[2025-08-21 04:18:02] 最终队列长度: 0
[2025-08-21 04:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 04:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 04:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:21:01] ✅ Redis连接成功
[2025-08-21 04:21:01] ✅ Redis连接成功
[2025-08-21 04:21:01] 当前队列长度: 0
[2025-08-21 04:21:01] 当前队列长度: 0
[2025-08-21 04:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:21:01] 最终队列长度: 0
[2025-08-21 04:21:01] 最终队列长度: 0
[2025-08-21 04:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:24:01] ✅ Redis连接成功
[2025-08-21 04:24:01] ✅ Redis连接成功
[2025-08-21 04:24:01] 当前队列长度: 0
[2025-08-21 04:24:01] 当前队列长度: 0
[2025-08-21 04:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:24:01] 最终队列长度: 0
[2025-08-21 04:24:01] 最终队列长度: 0
[2025-08-21 04:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:27:01] ✅ Redis连接成功
[2025-08-21 04:27:01] ✅ Redis连接成功
[2025-08-21 04:27:01] 当前队列长度: 0
[2025-08-21 04:27:01] 当前队列长度: 0
[2025-08-21 04:27:01] 准备入队: 订单31 - 18719677112 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单31 - 18719677112 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单32 - 15272998530 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单32 - 15272998530 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单33 - 18271716150 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单33 - 18271716150 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单35 - 15736504745 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单35 - 15736504745 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单36 - 13602194032 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单36 - 13602194032 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单38 - 15158864177 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单38 - 15158864177 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单39 - 18727705595 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单39 - 18727705595 - 进行中 - 50%
[2025-08-21 04:27:01] 准备入队: 订单40 - 13650574515 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单40 - 13650574515 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单41 - 15971771568 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单41 - 15971771568 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单42 - 13477205867 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单42 - 13477205867 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单43 - 15272268186 - 上号中 - 获取失败
[2025-08-21 04:27:01] 准备入队: 订单43 - 15272268186 - 上号中 - 获取失败
[2025-08-21 04:27:01] 扫描到11个订单，其中{count(Array)}个需要入队
[2025-08-21 04:27:01] 扫描到11个订单，其中{count(Array)}个需要入队
[2025-08-21 04:27:01] ✅ 成功入队11个订单
[2025-08-21 04:27:01] ✅ 成功入队11个订单
[2025-08-21 04:27:01] 更新11个订单状态为'待更新'
[2025-08-21 04:27:01] 更新11个订单状态为'待更新'
[2025-08-21 04:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:27:01] 最终队列长度: 11
[2025-08-21 04:27:01] 最终队列长度: 11
[2025-08-21 04:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:30:01] ✅ Redis连接成功
[2025-08-21 04:30:01] ✅ Redis连接成功
[2025-08-21 04:30:01] 当前队列长度: 0
[2025-08-21 04:30:01] 当前队列长度: 0
[2025-08-21 04:30:01] 准备入队: 订单43 - 15272268186 - 进行中 - 50%
[2025-08-21 04:30:01] 准备入队: 订单43 - 15272268186 - 进行中 - 50%
[2025-08-21 04:30:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 04:30:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 04:30:01] ✅ 成功入队1个订单
[2025-08-21 04:30:01] ✅ 成功入队1个订单
[2025-08-21 04:30:01] 更新1个订单状态为'待更新'
[2025-08-21 04:30:01] 更新1个订单状态为'待更新'
[2025-08-21 04:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:30:01] 最终队列长度: 1
[2025-08-21 04:30:01] 最终队列长度: 1
[2025-08-21 04:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:33:01] ✅ Redis连接成功
[2025-08-21 04:33:01] ✅ Redis连接成功
[2025-08-21 04:33:01] 当前队列长度: 0
[2025-08-21 04:33:01] 当前队列长度: 0
[2025-08-21 04:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:33:01] 最终队列长度: 0
[2025-08-21 04:33:01] 最终队列长度: 0
[2025-08-21 04:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:36:02] === 自动入队脚本开始执行 ===
[2025-08-21 04:36:02] === 自动入队脚本开始执行 ===
[2025-08-21 04:36:02] ✅ Redis连接成功
[2025-08-21 04:36:02] ✅ Redis连接成功
[2025-08-21 04:36:02] 当前队列长度: 0
[2025-08-21 04:36:02] 当前队列长度: 0
[2025-08-21 04:36:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:36:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 04:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 04:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:36:02] 最终队列长度: 0
[2025-08-21 04:36:02] 最终队列长度: 0
[2025-08-21 04:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 04:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 04:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:39:01] ✅ Redis连接成功
[2025-08-21 04:39:01] ✅ Redis连接成功
[2025-08-21 04:39:01] 当前队列长度: 0
[2025-08-21 04:39:01] 当前队列长度: 0
[2025-08-21 04:39:01] 准备入队: 订单44 - 18723589157 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单44 - 18723589157 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单45 - 15171068131 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单45 - 15171068131 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单46 - 13957632570 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单46 - 13957632570 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单47 - 13656580928 - 上号中 - 获取失败
[2025-08-21 04:39:01] 准备入队: 订单47 - 13656580928 - 上号中 - 获取失败
[2025-08-21 04:39:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 04:39:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 04:39:01] ✅ 成功入队4个订单
[2025-08-21 04:39:01] ✅ 成功入队4个订单
[2025-08-21 04:39:01] 更新4个订单状态为'待更新'
[2025-08-21 04:39:01] 更新4个订单状态为'待更新'
[2025-08-21 04:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:39:01] 最终队列长度: 4
[2025-08-21 04:39:01] 最终队列长度: 4
[2025-08-21 04:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:42:01] ✅ Redis连接成功
[2025-08-21 04:42:01] ✅ Redis连接成功
[2025-08-21 04:42:01] 当前队列长度: 0
[2025-08-21 04:42:01] 当前队列长度: 0
[2025-08-21 04:42:01] 准备入队: 订单46 - 13957632570 - 运行中 - 获取失败
[2025-08-21 04:42:01] 准备入队: 订单46 - 13957632570 - 运行中 - 获取失败
[2025-08-21 04:42:01] 准备入队: 订单47 - 13656580928 - 运行中 - 获取失败
[2025-08-21 04:42:01] 准备入队: 订单47 - 13656580928 - 运行中 - 获取失败
[2025-08-21 04:42:01] 扫描到2个订单，其中{count(Array)}个需要入队
[2025-08-21 04:42:01] 扫描到2个订单，其中{count(Array)}个需要入队
[2025-08-21 04:42:01] ✅ 成功入队2个订单
[2025-08-21 04:42:01] ✅ 成功入队2个订单
[2025-08-21 04:42:01] 更新2个订单状态为'待更新'
[2025-08-21 04:42:01] 更新2个订单状态为'待更新'
[2025-08-21 04:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:42:01] 最终队列长度: 2
[2025-08-21 04:42:01] 最终队列长度: 2
[2025-08-21 04:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:45:01] ✅ Redis连接成功
[2025-08-21 04:45:01] ✅ Redis连接成功
[2025-08-21 04:45:01] 当前队列长度: 0
[2025-08-21 04:45:01] 当前队列长度: 0
[2025-08-21 04:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:45:01] 最终队列长度: 0
[2025-08-21 04:45:01] 最终队列长度: 0
[2025-08-21 04:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:48:01] ✅ Redis连接成功
[2025-08-21 04:48:01] ✅ Redis连接成功
[2025-08-21 04:48:01] 当前队列长度: 0
[2025-08-21 04:48:01] 当前队列长度: 0
[2025-08-21 04:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:48:01] 最终队列长度: 0
[2025-08-21 04:48:01] 最终队列长度: 0
[2025-08-21 04:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:51:01] ✅ Redis连接成功
[2025-08-21 04:51:01] ✅ Redis连接成功
[2025-08-21 04:51:01] 当前队列长度: 0
[2025-08-21 04:51:01] 当前队列长度: 0
[2025-08-21 04:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:51:01] 最终队列长度: 0
[2025-08-21 04:51:01] 最终队列长度: 0
[2025-08-21 04:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:54:01] ✅ Redis连接成功
[2025-08-21 04:54:01] ✅ Redis连接成功
[2025-08-21 04:54:01] 当前队列长度: 0
[2025-08-21 04:54:01] 当前队列长度: 0
[2025-08-21 04:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:54:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:54:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:54:01] 最终队列长度: 0
[2025-08-21 04:54:01] 最终队列长度: 0
[2025-08-21 04:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 04:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 04:57:01] ✅ Redis连接成功
[2025-08-21 04:57:01] ✅ Redis连接成功
[2025-08-21 04:57:01] 当前队列长度: 0
[2025-08-21 04:57:01] 当前队列长度: 0
[2025-08-21 04:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 04:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 04:57:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:57:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 04:57:02] 最终队列长度: 0
[2025-08-21 04:57:02] 最终队列长度: 0
[2025-08-21 04:57:02] === 自动入队脚本执行完成 ===
[2025-08-21 04:57:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:00:01] ✅ Redis连接成功
[2025-08-21 05:00:01] ✅ Redis连接成功
[2025-08-21 05:00:01] 当前队列长度: 0
[2025-08-21 05:00:01] 当前队列长度: 0
[2025-08-21 05:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:00:02] 最终队列长度: 0
[2025-08-21 05:00:02] 最终队列长度: 0
[2025-08-21 05:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:03:01] ✅ Redis连接成功
[2025-08-21 05:03:01] ✅ Redis连接成功
[2025-08-21 05:03:01] 当前队列长度: 0
[2025-08-21 05:03:01] 当前队列长度: 0
[2025-08-21 05:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:03:01] 最终队列长度: 0
[2025-08-21 05:03:01] 最终队列长度: 0
[2025-08-21 05:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:06:02] === 自动入队脚本开始执行 ===
[2025-08-21 05:06:02] === 自动入队脚本开始执行 ===
[2025-08-21 05:06:02] ✅ Redis连接成功
[2025-08-21 05:06:02] ✅ Redis连接成功
[2025-08-21 05:06:02] 当前队列长度: 0
[2025-08-21 05:06:02] 当前队列长度: 0
[2025-08-21 05:06:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:06:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:06:02] ℹ️ 没有新订单需要入队
[2025-08-21 05:06:02] ℹ️ 没有新订单需要入队
[2025-08-21 05:06:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:06:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:06:02] 最终队列长度: 0
[2025-08-21 05:06:02] 最终队列长度: 0
[2025-08-21 05:06:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:06:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:09:01] ✅ Redis连接成功
[2025-08-21 05:09:01] ✅ Redis连接成功
[2025-08-21 05:09:01] 当前队列长度: 0
[2025-08-21 05:09:01] 当前队列长度: 0
[2025-08-21 05:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:09:01] 最终队列长度: 0
[2025-08-21 05:09:01] 最终队列长度: 0
[2025-08-21 05:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:12:01] ✅ Redis连接成功
[2025-08-21 05:12:01] ✅ Redis连接成功
[2025-08-21 05:12:01] 当前队列长度: 0
[2025-08-21 05:12:01] 当前队列长度: 0
[2025-08-21 05:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:12:01] 最终队列长度: 0
[2025-08-21 05:12:01] 最终队列长度: 0
[2025-08-21 05:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:15:01] ✅ Redis连接成功
[2025-08-21 05:15:01] ✅ Redis连接成功
[2025-08-21 05:15:01] 当前队列长度: 0
[2025-08-21 05:15:01] 当前队列长度: 0
[2025-08-21 05:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:15:01] 最终队列长度: 0
[2025-08-21 05:15:01] 最终队列长度: 0
[2025-08-21 05:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:18:01] ✅ Redis连接成功
[2025-08-21 05:18:01] ✅ Redis连接成功
[2025-08-21 05:18:01] 当前队列长度: 0
[2025-08-21 05:18:01] 当前队列长度: 0
[2025-08-21 05:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:18:01] 最终队列长度: 0
[2025-08-21 05:18:01] 最终队列长度: 0
[2025-08-21 05:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:21:01] ✅ Redis连接成功
[2025-08-21 05:21:01] ✅ Redis连接成功
[2025-08-21 05:21:01] 当前队列长度: 0
[2025-08-21 05:21:01] 当前队列长度: 0
[2025-08-21 05:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:21:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:21:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:21:02] 最终队列长度: 0
[2025-08-21 05:21:02] 最终队列长度: 0
[2025-08-21 05:21:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:21:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:24:01] ✅ Redis连接成功
[2025-08-21 05:24:01] ✅ Redis连接成功
[2025-08-21 05:24:01] 当前队列长度: 0
[2025-08-21 05:24:01] 当前队列长度: 0
[2025-08-21 05:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:24:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:24:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:24:02] 最终队列长度: 0
[2025-08-21 05:24:02] 最终队列长度: 0
[2025-08-21 05:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:27:01] ✅ Redis连接成功
[2025-08-21 05:27:01] ✅ Redis连接成功
[2025-08-21 05:27:01] 当前队列长度: 0
[2025-08-21 05:27:01] 当前队列长度: 0
[2025-08-21 05:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:27:01] 最终队列长度: 0
[2025-08-21 05:27:01] 最终队列长度: 0
[2025-08-21 05:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:30:01] ✅ Redis连接成功
[2025-08-21 05:30:01] ✅ Redis连接成功
[2025-08-21 05:30:01] 当前队列长度: 0
[2025-08-21 05:30:01] 当前队列长度: 0
[2025-08-21 05:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:30:02] 最终队列长度: 0
[2025-08-21 05:30:02] 最终队列长度: 0
[2025-08-21 05:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:33:01] ✅ Redis连接成功
[2025-08-21 05:33:01] ✅ Redis连接成功
[2025-08-21 05:33:01] 当前队列长度: 0
[2025-08-21 05:33:01] 当前队列长度: 0
[2025-08-21 05:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:33:01] 最终队列长度: 0
[2025-08-21 05:33:01] 最终队列长度: 0
[2025-08-21 05:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:36:01] ✅ Redis连接成功
[2025-08-21 05:36:01] ✅ Redis连接成功
[2025-08-21 05:36:01] 当前队列长度: 0
[2025-08-21 05:36:01] 当前队列长度: 0
[2025-08-21 05:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:36:01] 最终队列长度: 0
[2025-08-21 05:36:01] 最终队列长度: 0
[2025-08-21 05:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:39:01] ✅ Redis连接成功
[2025-08-21 05:39:01] ✅ Redis连接成功
[2025-08-21 05:39:01] 当前队列长度: 0
[2025-08-21 05:39:01] 当前队列长度: 0
[2025-08-21 05:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:39:01] 最终队列长度: 0
[2025-08-21 05:39:01] 最终队列长度: 0
[2025-08-21 05:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:42:01] ✅ Redis连接成功
[2025-08-21 05:42:01] ✅ Redis连接成功
[2025-08-21 05:42:01] 当前队列长度: 0
[2025-08-21 05:42:01] 当前队列长度: 0
[2025-08-21 05:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:42:01] 最终队列长度: 0
[2025-08-21 05:42:01] 最终队列长度: 0
[2025-08-21 05:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:45:01] ✅ Redis连接成功
[2025-08-21 05:45:01] ✅ Redis连接成功
[2025-08-21 05:45:01] 当前队列长度: 0
[2025-08-21 05:45:01] 当前队列长度: 0
[2025-08-21 05:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:45:01] 最终队列长度: 0
[2025-08-21 05:45:01] 最终队列长度: 0
[2025-08-21 05:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:48:01] ✅ Redis连接成功
[2025-08-21 05:48:01] ✅ Redis连接成功
[2025-08-21 05:48:01] 当前队列长度: 0
[2025-08-21 05:48:01] 当前队列长度: 0
[2025-08-21 05:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:48:01] 最终队列长度: 0
[2025-08-21 05:48:01] 最终队列长度: 0
[2025-08-21 05:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:51:01] ✅ Redis连接成功
[2025-08-21 05:51:01] ✅ Redis连接成功
[2025-08-21 05:51:01] 当前队列长度: 0
[2025-08-21 05:51:01] 当前队列长度: 0
[2025-08-21 05:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:51:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:51:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:51:02] 最终队列长度: 0
[2025-08-21 05:51:02] 最终队列长度: 0
[2025-08-21 05:51:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:51:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:54:01] ✅ Redis连接成功
[2025-08-21 05:54:01] ✅ Redis连接成功
[2025-08-21 05:54:01] 当前队列长度: 0
[2025-08-21 05:54:01] 当前队列长度: 0
[2025-08-21 05:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:54:02] 最终队列长度: 0
[2025-08-21 05:54:02] 最终队列长度: 0
[2025-08-21 05:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 05:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 05:57:01] ✅ Redis连接成功
[2025-08-21 05:57:01] ✅ Redis连接成功
[2025-08-21 05:57:01] 当前队列长度: 0
[2025-08-21 05:57:01] 当前队列长度: 0
[2025-08-21 05:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 05:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 05:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 05:57:01] 最终队列长度: 0
[2025-08-21 05:57:01] 最终队列长度: 0
[2025-08-21 05:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 05:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:00:01] ✅ Redis连接成功
[2025-08-21 06:00:01] ✅ Redis连接成功
[2025-08-21 06:00:01] 当前队列长度: 0
[2025-08-21 06:00:01] 当前队列长度: 0
[2025-08-21 06:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:00:01] 最终队列长度: 0
[2025-08-21 06:00:01] 最终队列长度: 0
[2025-08-21 06:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:03:01] ✅ Redis连接成功
[2025-08-21 06:03:01] ✅ Redis连接成功
[2025-08-21 06:03:01] 当前队列长度: 0
[2025-08-21 06:03:01] 当前队列长度: 0
[2025-08-21 06:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:03:01] 最终队列长度: 0
[2025-08-21 06:03:01] 最终队列长度: 0
[2025-08-21 06:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:06:01] ✅ Redis连接成功
[2025-08-21 06:06:01] ✅ Redis连接成功
[2025-08-21 06:06:01] 当前队列长度: 0
[2025-08-21 06:06:01] 当前队列长度: 0
[2025-08-21 06:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:06:01] 最终队列长度: 0
[2025-08-21 06:06:01] 最终队列长度: 0
[2025-08-21 06:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:09:01] ✅ Redis连接成功
[2025-08-21 06:09:01] ✅ Redis连接成功
[2025-08-21 06:09:01] 当前队列长度: 0
[2025-08-21 06:09:01] 当前队列长度: 0
[2025-08-21 06:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:09:01] 最终队列长度: 0
[2025-08-21 06:09:01] 最终队列长度: 0
[2025-08-21 06:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:12:01] ✅ Redis连接成功
[2025-08-21 06:12:01] ✅ Redis连接成功
[2025-08-21 06:12:01] 当前队列长度: 0
[2025-08-21 06:12:01] 当前队列长度: 0
[2025-08-21 06:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:12:01] 最终队列长度: 0
[2025-08-21 06:12:01] 最终队列长度: 0
[2025-08-21 06:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:15:01] ✅ Redis连接成功
[2025-08-21 06:15:01] ✅ Redis连接成功
[2025-08-21 06:15:01] 当前队列长度: 0
[2025-08-21 06:15:01] 当前队列长度: 0
[2025-08-21 06:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:15:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:15:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:15:02] 最终队列长度: 0
[2025-08-21 06:15:02] 最终队列长度: 0
[2025-08-21 06:15:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:15:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:18:01] ✅ Redis连接成功
[2025-08-21 06:18:01] ✅ Redis连接成功
[2025-08-21 06:18:01] 当前队列长度: 0
[2025-08-21 06:18:01] 当前队列长度: 0
[2025-08-21 06:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:18:01] 最终队列长度: 0
[2025-08-21 06:18:01] 最终队列长度: 0
[2025-08-21 06:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:21:01] ✅ Redis连接成功
[2025-08-21 06:21:01] ✅ Redis连接成功
[2025-08-21 06:21:01] 当前队列长度: 0
[2025-08-21 06:21:01] 当前队列长度: 0
[2025-08-21 06:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:21:01] 最终队列长度: 0
[2025-08-21 06:21:01] 最终队列长度: 0
[2025-08-21 06:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:24:02] === 自动入队脚本开始执行 ===
[2025-08-21 06:24:02] === 自动入队脚本开始执行 ===
[2025-08-21 06:24:02] ✅ Redis连接成功
[2025-08-21 06:24:02] ✅ Redis连接成功
[2025-08-21 06:24:02] 当前队列长度: 0
[2025-08-21 06:24:02] 当前队列长度: 0
[2025-08-21 06:24:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:24:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:24:02] ℹ️ 没有新订单需要入队
[2025-08-21 06:24:02] ℹ️ 没有新订单需要入队
[2025-08-21 06:24:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:24:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:24:02] 最终队列长度: 0
[2025-08-21 06:24:02] 最终队列长度: 0
[2025-08-21 06:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:27:01] ✅ Redis连接成功
[2025-08-21 06:27:01] ✅ Redis连接成功
[2025-08-21 06:27:01] 当前队列长度: 0
[2025-08-21 06:27:01] 当前队列长度: 0
[2025-08-21 06:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:27:01] 最终队列长度: 0
[2025-08-21 06:27:01] 最终队列长度: 0
[2025-08-21 06:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:30:01] ✅ Redis连接成功
[2025-08-21 06:30:01] ✅ Redis连接成功
[2025-08-21 06:30:01] 当前队列长度: 0
[2025-08-21 06:30:01] 当前队列长度: 0
[2025-08-21 06:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:30:01] 最终队列长度: 0
[2025-08-21 06:30:01] 最终队列长度: 0
[2025-08-21 06:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:33:01] ✅ Redis连接成功
[2025-08-21 06:33:01] ✅ Redis连接成功
[2025-08-21 06:33:01] 当前队列长度: 0
[2025-08-21 06:33:01] 当前队列长度: 0
[2025-08-21 06:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:33:01] 最终队列长度: 0
[2025-08-21 06:33:01] 最终队列长度: 0
[2025-08-21 06:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:36:01] ✅ Redis连接成功
[2025-08-21 06:36:01] ✅ Redis连接成功
[2025-08-21 06:36:01] 当前队列长度: 0
[2025-08-21 06:36:01] 当前队列长度: 0
[2025-08-21 06:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:36:01] 最终队列长度: 0
[2025-08-21 06:36:01] 最终队列长度: 0
[2025-08-21 06:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:39:01] ✅ Redis连接成功
[2025-08-21 06:39:01] ✅ Redis连接成功
[2025-08-21 06:39:01] 当前队列长度: 0
[2025-08-21 06:39:01] 当前队列长度: 0
[2025-08-21 06:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:39:01] 最终队列长度: 0
[2025-08-21 06:39:01] 最终队列长度: 0
[2025-08-21 06:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:42:01] ✅ Redis连接成功
[2025-08-21 06:42:01] ✅ Redis连接成功
[2025-08-21 06:42:01] 当前队列长度: 0
[2025-08-21 06:42:01] 当前队列长度: 0
[2025-08-21 06:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:42:01] 最终队列长度: 0
[2025-08-21 06:42:01] 最终队列长度: 0
[2025-08-21 06:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:45:01] ✅ Redis连接成功
[2025-08-21 06:45:01] ✅ Redis连接成功
[2025-08-21 06:45:01] 当前队列长度: 0
[2025-08-21 06:45:01] 当前队列长度: 0
[2025-08-21 06:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:45:01] 最终队列长度: 0
[2025-08-21 06:45:01] 最终队列长度: 0
[2025-08-21 06:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:48:01] ✅ Redis连接成功
[2025-08-21 06:48:01] ✅ Redis连接成功
[2025-08-21 06:48:01] 当前队列长度: 0
[2025-08-21 06:48:01] 当前队列长度: 0
[2025-08-21 06:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:48:01] 最终队列长度: 0
[2025-08-21 06:48:01] 最终队列长度: 0
[2025-08-21 06:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:51:02] === 自动入队脚本开始执行 ===
[2025-08-21 06:51:02] === 自动入队脚本开始执行 ===
[2025-08-21 06:51:02] ✅ Redis连接成功
[2025-08-21 06:51:02] ✅ Redis连接成功
[2025-08-21 06:51:02] 当前队列长度: 0
[2025-08-21 06:51:02] 当前队列长度: 0
[2025-08-21 06:51:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:51:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:51:02] ℹ️ 没有新订单需要入队
[2025-08-21 06:51:02] ℹ️ 没有新订单需要入队
[2025-08-21 06:51:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:51:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:51:02] 最终队列长度: 0
[2025-08-21 06:51:02] 最终队列长度: 0
[2025-08-21 06:51:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:51:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:54:01] ✅ Redis连接成功
[2025-08-21 06:54:01] ✅ Redis连接成功
[2025-08-21 06:54:01] 当前队列长度: 0
[2025-08-21 06:54:01] 当前队列长度: 0
[2025-08-21 06:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:54:02] 最终队列长度: 0
[2025-08-21 06:54:02] 最终队列长度: 0
[2025-08-21 06:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 06:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 06:57:01] ✅ Redis连接成功
[2025-08-21 06:57:01] ✅ Redis连接成功
[2025-08-21 06:57:01] 当前队列长度: 0
[2025-08-21 06:57:01] 当前队列长度: 0
[2025-08-21 06:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 06:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 06:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 06:57:01] 最终队列长度: 0
[2025-08-21 06:57:01] 最终队列长度: 0
[2025-08-21 06:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 06:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:00:02] === 自动入队脚本开始执行 ===
[2025-08-21 07:00:02] === 自动入队脚本开始执行 ===
[2025-08-21 07:00:02] ✅ Redis连接成功
[2025-08-21 07:00:02] ✅ Redis连接成功
[2025-08-21 07:00:02] 当前队列长度: 0
[2025-08-21 07:00:02] 当前队列长度: 0
[2025-08-21 07:00:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:00:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 07:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 07:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:00:02] 最终队列长度: 0
[2025-08-21 07:00:02] 最终队列长度: 0
[2025-08-21 07:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:03:01] ✅ Redis连接成功
[2025-08-21 07:03:01] ✅ Redis连接成功
[2025-08-21 07:03:01] 当前队列长度: 0
[2025-08-21 07:03:01] 当前队列长度: 0
[2025-08-21 07:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:03:01] 最终队列长度: 0
[2025-08-21 07:03:01] 最终队列长度: 0
[2025-08-21 07:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:06:01] ✅ Redis连接成功
[2025-08-21 07:06:01] ✅ Redis连接成功
[2025-08-21 07:06:01] 当前队列长度: 0
[2025-08-21 07:06:01] 当前队列长度: 0
[2025-08-21 07:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:06:01] 最终队列长度: 0
[2025-08-21 07:06:01] 最终队列长度: 0
[2025-08-21 07:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:09:01] ✅ Redis连接成功
[2025-08-21 07:09:01] ✅ Redis连接成功
[2025-08-21 07:09:01] 当前队列长度: 0
[2025-08-21 07:09:01] 当前队列长度: 0
[2025-08-21 07:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:09:01] 最终队列长度: 0
[2025-08-21 07:09:01] 最终队列长度: 0
[2025-08-21 07:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:12:01] ✅ Redis连接成功
[2025-08-21 07:12:01] ✅ Redis连接成功
[2025-08-21 07:12:01] 当前队列长度: 0
[2025-08-21 07:12:01] 当前队列长度: 0
[2025-08-21 07:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:12:01] 最终队列长度: 0
[2025-08-21 07:12:01] 最终队列长度: 0
[2025-08-21 07:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:15:01] ✅ Redis连接成功
[2025-08-21 07:15:01] ✅ Redis连接成功
[2025-08-21 07:15:01] 当前队列长度: 0
[2025-08-21 07:15:01] 当前队列长度: 0
[2025-08-21 07:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:15:01] 最终队列长度: 0
[2025-08-21 07:15:01] 最终队列长度: 0
[2025-08-21 07:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:18:01] ✅ Redis连接成功
[2025-08-21 07:18:01] ✅ Redis连接成功
[2025-08-21 07:18:01] 当前队列长度: 0
[2025-08-21 07:18:01] 当前队列长度: 0
[2025-08-21 07:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:18:01] 最终队列长度: 0
[2025-08-21 07:18:01] 最终队列长度: 0
[2025-08-21 07:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:21:01] ✅ Redis连接成功
[2025-08-21 07:21:01] ✅ Redis连接成功
[2025-08-21 07:21:01] 当前队列长度: 0
[2025-08-21 07:21:01] 当前队列长度: 0
[2025-08-21 07:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:21:01] 最终队列长度: 0
[2025-08-21 07:21:01] 最终队列长度: 0
[2025-08-21 07:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:24:01] ✅ Redis连接成功
[2025-08-21 07:24:01] ✅ Redis连接成功
[2025-08-21 07:24:01] 当前队列长度: 0
[2025-08-21 07:24:01] 当前队列长度: 0
[2025-08-21 07:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:24:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:24:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:24:02] 最终队列长度: 0
[2025-08-21 07:24:02] 最终队列长度: 0
[2025-08-21 07:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:27:01] ✅ Redis连接成功
[2025-08-21 07:27:01] ✅ Redis连接成功
[2025-08-21 07:27:01] 当前队列长度: 0
[2025-08-21 07:27:01] 当前队列长度: 0
[2025-08-21 07:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:27:01] 最终队列长度: 0
[2025-08-21 07:27:01] 最终队列长度: 0
[2025-08-21 07:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:30:02] === 自动入队脚本开始执行 ===
[2025-08-21 07:30:02] === 自动入队脚本开始执行 ===
[2025-08-21 07:30:02] ✅ Redis连接成功
[2025-08-21 07:30:02] ✅ Redis连接成功
[2025-08-21 07:30:02] 当前队列长度: 0
[2025-08-21 07:30:02] 当前队列长度: 0
[2025-08-21 07:30:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:30:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:30:02] ℹ️ 没有新订单需要入队
[2025-08-21 07:30:02] ℹ️ 没有新订单需要入队
[2025-08-21 07:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:30:02] 最终队列长度: 0
[2025-08-21 07:30:02] 最终队列长度: 0
[2025-08-21 07:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:33:01] ✅ Redis连接成功
[2025-08-21 07:33:01] ✅ Redis连接成功
[2025-08-21 07:33:01] 当前队列长度: 0
[2025-08-21 07:33:01] 当前队列长度: 0
[2025-08-21 07:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:33:01] 最终队列长度: 0
[2025-08-21 07:33:01] 最终队列长度: 0
[2025-08-21 07:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:36:01] ✅ Redis连接成功
[2025-08-21 07:36:01] ✅ Redis连接成功
[2025-08-21 07:36:01] 当前队列长度: 0
[2025-08-21 07:36:01] 当前队列长度: 0
[2025-08-21 07:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:36:01] 最终队列长度: 0
[2025-08-21 07:36:01] 最终队列长度: 0
[2025-08-21 07:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:39:01] ✅ Redis连接成功
[2025-08-21 07:39:01] ✅ Redis连接成功
[2025-08-21 07:39:01] 当前队列长度: 0
[2025-08-21 07:39:01] 当前队列长度: 0
[2025-08-21 07:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:39:01] 最终队列长度: 0
[2025-08-21 07:39:01] 最终队列长度: 0
[2025-08-21 07:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:42:01] ✅ Redis连接成功
[2025-08-21 07:42:01] ✅ Redis连接成功
[2025-08-21 07:42:01] 当前队列长度: 0
[2025-08-21 07:42:01] 当前队列长度: 0
[2025-08-21 07:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:42:01] 最终队列长度: 0
[2025-08-21 07:42:01] 最终队列长度: 0
[2025-08-21 07:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:45:01] ✅ Redis连接成功
[2025-08-21 07:45:01] ✅ Redis连接成功
[2025-08-21 07:45:01] 当前队列长度: 0
[2025-08-21 07:45:01] 当前队列长度: 0
[2025-08-21 07:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:45:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:45:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:45:02] 最终队列长度: 0
[2025-08-21 07:45:02] 最终队列长度: 0
[2025-08-21 07:45:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:45:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:48:01] ✅ Redis连接成功
[2025-08-21 07:48:01] ✅ Redis连接成功
[2025-08-21 07:48:01] 当前队列长度: 0
[2025-08-21 07:48:01] 当前队列长度: 0
[2025-08-21 07:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:48:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:48:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:48:02] 最终队列长度: 0
[2025-08-21 07:48:02] 最终队列长度: 0
[2025-08-21 07:48:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:48:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:51:01] ✅ Redis连接成功
[2025-08-21 07:51:01] ✅ Redis连接成功
[2025-08-21 07:51:01] 当前队列长度: 0
[2025-08-21 07:51:01] 当前队列长度: 0
[2025-08-21 07:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:51:01] 最终队列长度: 0
[2025-08-21 07:51:01] 最终队列长度: 0
[2025-08-21 07:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:54:01] ✅ Redis连接成功
[2025-08-21 07:54:01] ✅ Redis连接成功
[2025-08-21 07:54:01] 当前队列长度: 0
[2025-08-21 07:54:01] 当前队列长度: 0
[2025-08-21 07:54:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:54:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:54:02] ℹ️ 没有新订单需要入队
[2025-08-21 07:54:02] ℹ️ 没有新订单需要入队
[2025-08-21 07:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:54:02] 最终队列长度: 0
[2025-08-21 07:54:02] 最终队列长度: 0
[2025-08-21 07:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 07:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 07:57:01] ✅ Redis连接成功
[2025-08-21 07:57:01] ✅ Redis连接成功
[2025-08-21 07:57:01] 当前队列长度: 0
[2025-08-21 07:57:01] 当前队列长度: 0
[2025-08-21 07:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 07:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 07:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 07:57:01] 最终队列长度: 0
[2025-08-21 07:57:01] 最终队列长度: 0
[2025-08-21 07:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 07:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:00:01] ✅ Redis连接成功
[2025-08-21 08:00:01] ✅ Redis连接成功
[2025-08-21 08:00:01] 当前队列长度: 0
[2025-08-21 08:00:01] 当前队列长度: 0
[2025-08-21 08:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:00:01] 最终队列长度: 0
[2025-08-21 08:00:01] 最终队列长度: 0
[2025-08-21 08:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:03:01] ✅ Redis连接成功
[2025-08-21 08:03:01] ✅ Redis连接成功
[2025-08-21 08:03:01] 当前队列长度: 0
[2025-08-21 08:03:01] 当前队列长度: 0
[2025-08-21 08:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:03:01] 最终队列长度: 0
[2025-08-21 08:03:01] 最终队列长度: 0
[2025-08-21 08:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:06:01] ✅ Redis连接成功
[2025-08-21 08:06:01] ✅ Redis连接成功
[2025-08-21 08:06:01] 当前队列长度: 0
[2025-08-21 08:06:01] 当前队列长度: 0
[2025-08-21 08:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:06:01] 最终队列长度: 0
[2025-08-21 08:06:01] 最终队列长度: 0
[2025-08-21 08:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:09:01] ✅ Redis连接成功
[2025-08-21 08:09:01] ✅ Redis连接成功
[2025-08-21 08:09:01] 当前队列长度: 0
[2025-08-21 08:09:01] 当前队列长度: 0
[2025-08-21 08:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:09:01] 最终队列长度: 0
[2025-08-21 08:09:01] 最终队列长度: 0
[2025-08-21 08:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:12:01] ✅ Redis连接成功
[2025-08-21 08:12:01] ✅ Redis连接成功
[2025-08-21 08:12:01] 当前队列长度: 0
[2025-08-21 08:12:01] 当前队列长度: 0
[2025-08-21 08:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:12:01] 最终队列长度: 0
[2025-08-21 08:12:01] 最终队列长度: 0
[2025-08-21 08:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:15:01] ✅ Redis连接成功
[2025-08-21 08:15:01] ✅ Redis连接成功
[2025-08-21 08:15:01] 当前队列长度: 0
[2025-08-21 08:15:01] 当前队列长度: 0
[2025-08-21 08:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:15:01] 最终队列长度: 0
[2025-08-21 08:15:01] 最终队列长度: 0
[2025-08-21 08:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:18:01] ✅ Redis连接成功
[2025-08-21 08:18:01] ✅ Redis连接成功
[2025-08-21 08:18:01] 当前队列长度: 0
[2025-08-21 08:18:01] 当前队列长度: 0
[2025-08-21 08:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:18:02] 最终队列长度: 0
[2025-08-21 08:18:02] 最终队列长度: 0
[2025-08-21 08:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:21:01] ✅ Redis连接成功
[2025-08-21 08:21:01] ✅ Redis连接成功
[2025-08-21 08:21:01] 当前队列长度: 0
[2025-08-21 08:21:01] 当前队列长度: 0
[2025-08-21 08:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:21:01] 最终队列长度: 0
[2025-08-21 08:21:01] 最终队列长度: 0
[2025-08-21 08:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:24:01] ✅ Redis连接成功
[2025-08-21 08:24:01] ✅ Redis连接成功
[2025-08-21 08:24:01] 当前队列长度: 0
[2025-08-21 08:24:01] 当前队列长度: 0
[2025-08-21 08:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:24:01] 最终队列长度: 0
[2025-08-21 08:24:01] 最终队列长度: 0
[2025-08-21 08:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:27:01] ✅ Redis连接成功
[2025-08-21 08:27:01] ✅ Redis连接成功
[2025-08-21 08:27:01] 当前队列长度: 0
[2025-08-21 08:27:01] 当前队列长度: 0
[2025-08-21 08:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:27:01] 最终队列长度: 0
[2025-08-21 08:27:01] 最终队列长度: 0
[2025-08-21 08:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:30:01] ✅ Redis连接成功
[2025-08-21 08:30:01] ✅ Redis连接成功
[2025-08-21 08:30:01] 当前队列长度: 0
[2025-08-21 08:30:01] 当前队列长度: 0
[2025-08-21 08:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:30:02] 最终队列长度: 0
[2025-08-21 08:30:02] 最终队列长度: 0
[2025-08-21 08:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:33:01] ✅ Redis连接成功
[2025-08-21 08:33:01] ✅ Redis连接成功
[2025-08-21 08:33:01] 当前队列长度: 0
[2025-08-21 08:33:01] 当前队列长度: 0
[2025-08-21 08:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:33:01] 最终队列长度: 0
[2025-08-21 08:33:01] 最终队列长度: 0
[2025-08-21 08:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:36:01] ✅ Redis连接成功
[2025-08-21 08:36:01] ✅ Redis连接成功
[2025-08-21 08:36:01] 当前队列长度: 0
[2025-08-21 08:36:01] 当前队列长度: 0
[2025-08-21 08:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:36:02] 最终队列长度: 0
[2025-08-21 08:36:02] 最终队列长度: 0
[2025-08-21 08:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:39:01] ✅ Redis连接成功
[2025-08-21 08:39:01] ✅ Redis连接成功
[2025-08-21 08:39:01] 当前队列长度: 0
[2025-08-21 08:39:01] 当前队列长度: 0
[2025-08-21 08:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:39:01] 最终队列长度: 0
[2025-08-21 08:39:01] 最终队列长度: 0
[2025-08-21 08:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:42:01] ✅ Redis连接成功
[2025-08-21 08:42:01] ✅ Redis连接成功
[2025-08-21 08:42:01] 当前队列长度: 0
[2025-08-21 08:42:01] 当前队列长度: 0
[2025-08-21 08:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:42:01] 最终队列长度: 0
[2025-08-21 08:42:01] 最终队列长度: 0
[2025-08-21 08:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:45:01] ✅ Redis连接成功
[2025-08-21 08:45:01] ✅ Redis连接成功
[2025-08-21 08:45:01] 当前队列长度: 0
[2025-08-21 08:45:01] 当前队列长度: 0
[2025-08-21 08:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:45:01] 最终队列长度: 0
[2025-08-21 08:45:01] 最终队列长度: 0
[2025-08-21 08:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:48:01] ✅ Redis连接成功
[2025-08-21 08:48:01] ✅ Redis连接成功
[2025-08-21 08:48:01] 当前队列长度: 0
[2025-08-21 08:48:01] 当前队列长度: 0
[2025-08-21 08:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:48:01] 最终队列长度: 0
[2025-08-21 08:48:01] 最终队列长度: 0
[2025-08-21 08:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:51:01] ✅ Redis连接成功
[2025-08-21 08:51:01] ✅ Redis连接成功
[2025-08-21 08:51:01] 当前队列长度: 0
[2025-08-21 08:51:01] 当前队列长度: 0
[2025-08-21 08:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:51:01] 最终队列长度: 0
[2025-08-21 08:51:01] 最终队列长度: 0
[2025-08-21 08:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:54:01] ✅ Redis连接成功
[2025-08-21 08:54:01] ✅ Redis连接成功
[2025-08-21 08:54:01] 当前队列长度: 0
[2025-08-21 08:54:01] 当前队列长度: 0
[2025-08-21 08:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:54:02] 最终队列长度: 0
[2025-08-21 08:54:02] 最终队列长度: 0
[2025-08-21 08:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 08:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 08:57:01] ✅ Redis连接成功
[2025-08-21 08:57:01] ✅ Redis连接成功
[2025-08-21 08:57:01] 当前队列长度: 0
[2025-08-21 08:57:01] 当前队列长度: 0
[2025-08-21 08:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 08:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 08:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 08:57:01] 最终队列长度: 0
[2025-08-21 08:57:01] 最终队列长度: 0
[2025-08-21 08:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 08:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:00:01] ✅ Redis连接成功
[2025-08-21 09:00:01] ✅ Redis连接成功
[2025-08-21 09:00:01] 当前队列长度: 0
[2025-08-21 09:00:01] 当前队列长度: 0
[2025-08-21 09:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:00:02] 最终队列长度: 0
[2025-08-21 09:00:02] 最终队列长度: 0
[2025-08-21 09:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:03:01] ✅ Redis连接成功
[2025-08-21 09:03:01] ✅ Redis连接成功
[2025-08-21 09:03:01] 当前队列长度: 0
[2025-08-21 09:03:01] 当前队列长度: 0
[2025-08-21 09:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:03:01] 最终队列长度: 0
[2025-08-21 09:03:01] 最终队列长度: 0
[2025-08-21 09:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:06:01] ✅ Redis连接成功
[2025-08-21 09:06:01] ✅ Redis连接成功
[2025-08-21 09:06:01] 当前队列长度: 0
[2025-08-21 09:06:01] 当前队列长度: 0
[2025-08-21 09:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:06:01] 最终队列长度: 0
[2025-08-21 09:06:01] 最终队列长度: 0
[2025-08-21 09:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:09:01] ✅ Redis连接成功
[2025-08-21 09:09:01] ✅ Redis连接成功
[2025-08-21 09:09:01] 当前队列长度: 0
[2025-08-21 09:09:01] 当前队列长度: 0
[2025-08-21 09:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:09:01] 最终队列长度: 0
[2025-08-21 09:09:01] 最终队列长度: 0
[2025-08-21 09:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:12:01] ✅ Redis连接成功
[2025-08-21 09:12:01] ✅ Redis连接成功
[2025-08-21 09:12:01] 当前队列长度: 0
[2025-08-21 09:12:01] 当前队列长度: 0
[2025-08-21 09:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:12:01] 最终队列长度: 0
[2025-08-21 09:12:01] 最终队列长度: 0
[2025-08-21 09:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:15:02] === 自动入队脚本开始执行 ===
[2025-08-21 09:15:02] === 自动入队脚本开始执行 ===
[2025-08-21 09:15:02] ✅ Redis连接成功
[2025-08-21 09:15:02] ✅ Redis连接成功
[2025-08-21 09:15:02] 当前队列长度: 0
[2025-08-21 09:15:02] 当前队列长度: 0
[2025-08-21 09:15:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:15:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:15:02] ℹ️ 没有新订单需要入队
[2025-08-21 09:15:02] ℹ️ 没有新订单需要入队
[2025-08-21 09:15:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:15:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:15:02] 最终队列长度: 0
[2025-08-21 09:15:02] 最终队列长度: 0
[2025-08-21 09:15:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:15:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:18:01] ✅ Redis连接成功
[2025-08-21 09:18:01] ✅ Redis连接成功
[2025-08-21 09:18:01] 当前队列长度: 0
[2025-08-21 09:18:01] 当前队列长度: 0
[2025-08-21 09:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:18:02] 最终队列长度: 0
[2025-08-21 09:18:02] 最终队列长度: 0
[2025-08-21 09:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:21:01] ✅ Redis连接成功
[2025-08-21 09:21:01] ✅ Redis连接成功
[2025-08-21 09:21:01] 当前队列长度: 0
[2025-08-21 09:21:01] 当前队列长度: 0
[2025-08-21 09:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:21:01] 最终队列长度: 0
[2025-08-21 09:21:01] 最终队列长度: 0
[2025-08-21 09:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:24:01] ✅ Redis连接成功
[2025-08-21 09:24:01] ✅ Redis连接成功
[2025-08-21 09:24:01] 当前队列长度: 0
[2025-08-21 09:24:01] 当前队列长度: 0
[2025-08-21 09:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:24:01] 最终队列长度: 0
[2025-08-21 09:24:01] 最终队列长度: 0
[2025-08-21 09:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:27:01] ✅ Redis连接成功
[2025-08-21 09:27:01] ✅ Redis连接成功
[2025-08-21 09:27:01] 当前队列长度: 0
[2025-08-21 09:27:01] 当前队列长度: 0
[2025-08-21 09:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:27:01] 最终队列长度: 0
[2025-08-21 09:27:01] 最终队列长度: 0
[2025-08-21 09:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:30:01] ✅ Redis连接成功
[2025-08-21 09:30:01] ✅ Redis连接成功
[2025-08-21 09:30:01] 当前队列长度: 0
[2025-08-21 09:30:01] 当前队列长度: 0
[2025-08-21 09:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:30:01] 最终队列长度: 0
[2025-08-21 09:30:01] 最终队列长度: 0
[2025-08-21 09:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:33:01] ✅ Redis连接成功
[2025-08-21 09:33:01] ✅ Redis连接成功
[2025-08-21 09:33:01] 当前队列长度: 0
[2025-08-21 09:33:01] 当前队列长度: 0
[2025-08-21 09:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:33:01] 最终队列长度: 0
[2025-08-21 09:33:01] 最终队列长度: 0
[2025-08-21 09:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:36:02] === 自动入队脚本开始执行 ===
[2025-08-21 09:36:02] === 自动入队脚本开始执行 ===
[2025-08-21 09:36:02] ✅ Redis连接成功
[2025-08-21 09:36:02] ✅ Redis连接成功
[2025-08-21 09:36:02] 当前队列长度: 0
[2025-08-21 09:36:02] 当前队列长度: 0
[2025-08-21 09:36:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:36:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 09:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 09:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:36:02] 最终队列长度: 0
[2025-08-21 09:36:02] 最终队列长度: 0
[2025-08-21 09:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:39:01] ✅ Redis连接成功
[2025-08-21 09:39:01] ✅ Redis连接成功
[2025-08-21 09:39:01] 当前队列长度: 0
[2025-08-21 09:39:01] 当前队列长度: 0
[2025-08-21 09:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:39:01] 最终队列长度: 0
[2025-08-21 09:39:01] 最终队列长度: 0
[2025-08-21 09:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:42:01] ✅ Redis连接成功
[2025-08-21 09:42:01] ✅ Redis连接成功
[2025-08-21 09:42:01] 当前队列长度: 0
[2025-08-21 09:42:01] 当前队列长度: 0
[2025-08-21 09:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:42:01] 最终队列长度: 0
[2025-08-21 09:42:01] 最终队列长度: 0
[2025-08-21 09:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:45:01] ✅ Redis连接成功
[2025-08-21 09:45:01] ✅ Redis连接成功
[2025-08-21 09:45:01] 当前队列长度: 0
[2025-08-21 09:45:01] 当前队列长度: 0
[2025-08-21 09:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:45:01] 最终队列长度: 0
[2025-08-21 09:45:01] 最终队列长度: 0
[2025-08-21 09:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:48:01] ✅ Redis连接成功
[2025-08-21 09:48:01] ✅ Redis连接成功
[2025-08-21 09:48:01] 当前队列长度: 0
[2025-08-21 09:48:01] 当前队列长度: 0
[2025-08-21 09:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:48:01] 最终队列长度: 0
[2025-08-21 09:48:01] 最终队列长度: 0
[2025-08-21 09:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:51:01] ✅ Redis连接成功
[2025-08-21 09:51:01] ✅ Redis连接成功
[2025-08-21 09:51:01] 当前队列长度: 0
[2025-08-21 09:51:01] 当前队列长度: 0
[2025-08-21 09:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:51:01] 最终队列长度: 0
[2025-08-21 09:51:01] 最终队列长度: 0
[2025-08-21 09:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:54:02] === 自动入队脚本开始执行 ===
[2025-08-21 09:54:02] === 自动入队脚本开始执行 ===
[2025-08-21 09:54:02] ✅ Redis连接成功
[2025-08-21 09:54:02] ✅ Redis连接成功
[2025-08-21 09:54:02] 当前队列长度: 0
[2025-08-21 09:54:02] 当前队列长度: 0
[2025-08-21 09:54:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:54:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:54:02] ℹ️ 没有新订单需要入队
[2025-08-21 09:54:02] ℹ️ 没有新订单需要入队
[2025-08-21 09:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:54:02] 最终队列长度: 0
[2025-08-21 09:54:02] 最终队列长度: 0
[2025-08-21 09:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 09:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 09:57:01] ✅ Redis连接成功
[2025-08-21 09:57:01] ✅ Redis连接成功
[2025-08-21 09:57:01] 当前队列长度: 0
[2025-08-21 09:57:01] 当前队列长度: 0
[2025-08-21 09:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 09:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 09:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 09:57:01] 最终队列长度: 0
[2025-08-21 09:57:01] 最终队列长度: 0
[2025-08-21 09:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 09:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:00:01] ✅ Redis连接成功
[2025-08-21 10:00:01] ✅ Redis连接成功
[2025-08-21 10:00:01] 当前队列长度: 0
[2025-08-21 10:00:01] 当前队列长度: 0
[2025-08-21 10:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:00:01] 最终队列长度: 0
[2025-08-21 10:00:01] 最终队列长度: 0
[2025-08-21 10:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:03:01] ✅ Redis连接成功
[2025-08-21 10:03:01] ✅ Redis连接成功
[2025-08-21 10:03:01] 当前队列长度: 0
[2025-08-21 10:03:01] 当前队列长度: 0
[2025-08-21 10:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:03:01] 最终队列长度: 0
[2025-08-21 10:03:01] 最终队列长度: 0
[2025-08-21 10:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:06:01] ✅ Redis连接成功
[2025-08-21 10:06:01] ✅ Redis连接成功
[2025-08-21 10:06:01] 当前队列长度: 0
[2025-08-21 10:06:01] 当前队列长度: 0
[2025-08-21 10:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:06:01] 最终队列长度: 0
[2025-08-21 10:06:01] 最终队列长度: 0
[2025-08-21 10:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:09:01] ✅ Redis连接成功
[2025-08-21 10:09:01] ✅ Redis连接成功
[2025-08-21 10:09:01] 当前队列长度: 0
[2025-08-21 10:09:01] 当前队列长度: 0
[2025-08-21 10:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:09:01] 最终队列长度: 0
[2025-08-21 10:09:01] 最终队列长度: 0
[2025-08-21 10:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:12:01] ✅ Redis连接成功
[2025-08-21 10:12:01] ✅ Redis连接成功
[2025-08-21 10:12:01] 当前队列长度: 0
[2025-08-21 10:12:01] 当前队列长度: 0
[2025-08-21 10:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:12:01] 最终队列长度: 0
[2025-08-21 10:12:01] 最终队列长度: 0
[2025-08-21 10:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:15:01] ✅ Redis连接成功
[2025-08-21 10:15:01] ✅ Redis连接成功
[2025-08-21 10:15:01] 当前队列长度: 0
[2025-08-21 10:15:01] 当前队列长度: 0
[2025-08-21 10:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:15:01] 最终队列长度: 0
[2025-08-21 10:15:01] 最终队列长度: 0
[2025-08-21 10:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:18:02] === 自动入队脚本开始执行 ===
[2025-08-21 10:18:02] === 自动入队脚本开始执行 ===
[2025-08-21 10:18:02] ✅ Redis连接成功
[2025-08-21 10:18:02] ✅ Redis连接成功
[2025-08-21 10:18:02] 当前队列长度: 0
[2025-08-21 10:18:02] 当前队列长度: 0
[2025-08-21 10:18:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:18:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:18:02] ℹ️ 没有新订单需要入队
[2025-08-21 10:18:02] ℹ️ 没有新订单需要入队
[2025-08-21 10:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:18:02] 最终队列长度: 0
[2025-08-21 10:18:02] 最终队列长度: 0
[2025-08-21 10:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:21:01] ✅ Redis连接成功
[2025-08-21 10:21:01] ✅ Redis连接成功
[2025-08-21 10:21:01] 当前队列长度: 0
[2025-08-21 10:21:01] 当前队列长度: 0
[2025-08-21 10:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:21:01] 最终队列长度: 0
[2025-08-21 10:21:01] 最终队列长度: 0
[2025-08-21 10:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:24:01] ✅ Redis连接成功
[2025-08-21 10:24:01] ✅ Redis连接成功
[2025-08-21 10:24:01] 当前队列长度: 0
[2025-08-21 10:24:01] 当前队列长度: 0
[2025-08-21 10:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:24:01] 最终队列长度: 0
[2025-08-21 10:24:01] 最终队列长度: 0
[2025-08-21 10:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:27:01] ✅ Redis连接成功
[2025-08-21 10:27:01] ✅ Redis连接成功
[2025-08-21 10:27:01] 当前队列长度: 0
[2025-08-21 10:27:01] 当前队列长度: 0
[2025-08-21 10:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:27:01] 最终队列长度: 0
[2025-08-21 10:27:01] 最终队列长度: 0
[2025-08-21 10:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:30:01] ✅ Redis连接成功
[2025-08-21 10:30:01] ✅ Redis连接成功
[2025-08-21 10:30:01] 当前队列长度: 0
[2025-08-21 10:30:01] 当前队列长度: 0
[2025-08-21 10:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:30:01] 最终队列长度: 0
[2025-08-21 10:30:01] 最终队列长度: 0
[2025-08-21 10:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:33:01] ✅ Redis连接成功
[2025-08-21 10:33:01] ✅ Redis连接成功
[2025-08-21 10:33:01] 当前队列长度: 0
[2025-08-21 10:33:01] 当前队列长度: 0
[2025-08-21 10:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:33:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:33:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:33:02] 最终队列长度: 0
[2025-08-21 10:33:02] 最终队列长度: 0
[2025-08-21 10:33:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:33:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:36:01] ✅ Redis连接成功
[2025-08-21 10:36:01] ✅ Redis连接成功
[2025-08-21 10:36:01] 当前队列长度: 0
[2025-08-21 10:36:01] 当前队列长度: 0
[2025-08-21 10:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:36:01] 最终队列长度: 0
[2025-08-21 10:36:01] 最终队列长度: 0
[2025-08-21 10:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:39:01] ✅ Redis连接成功
[2025-08-21 10:39:01] ✅ Redis连接成功
[2025-08-21 10:39:01] 当前队列长度: 0
[2025-08-21 10:39:01] 当前队列长度: 0
[2025-08-21 10:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:39:01] 最终队列长度: 0
[2025-08-21 10:39:01] 最终队列长度: 0
[2025-08-21 10:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:42:02] === 自动入队脚本开始执行 ===
[2025-08-21 10:42:02] === 自动入队脚本开始执行 ===
[2025-08-21 10:42:02] ✅ Redis连接成功
[2025-08-21 10:42:02] ✅ Redis连接成功
[2025-08-21 10:42:02] 当前队列长度: 0
[2025-08-21 10:42:02] 当前队列长度: 0
[2025-08-21 10:42:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:42:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:42:02] ℹ️ 没有新订单需要入队
[2025-08-21 10:42:02] ℹ️ 没有新订单需要入队
[2025-08-21 10:42:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:42:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:42:02] 最终队列长度: 0
[2025-08-21 10:42:02] 最终队列长度: 0
[2025-08-21 10:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:45:01] ✅ Redis连接成功
[2025-08-21 10:45:01] ✅ Redis连接成功
[2025-08-21 10:45:01] 当前队列长度: 0
[2025-08-21 10:45:01] 当前队列长度: 0
[2025-08-21 10:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:45:01] 最终队列长度: 0
[2025-08-21 10:45:01] 最终队列长度: 0
[2025-08-21 10:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:48:01] ✅ Redis连接成功
[2025-08-21 10:48:01] ✅ Redis连接成功
[2025-08-21 10:48:01] 当前队列长度: 0
[2025-08-21 10:48:01] 当前队列长度: 0
[2025-08-21 10:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:48:01] 最终队列长度: 0
[2025-08-21 10:48:01] 最终队列长度: 0
[2025-08-21 10:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:51:01] ✅ Redis连接成功
[2025-08-21 10:51:01] ✅ Redis连接成功
[2025-08-21 10:51:01] 当前队列长度: 0
[2025-08-21 10:51:01] 当前队列长度: 0
[2025-08-21 10:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:51:01] 最终队列长度: 0
[2025-08-21 10:51:01] 最终队列长度: 0
[2025-08-21 10:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:54:01] ✅ Redis连接成功
[2025-08-21 10:54:01] ✅ Redis连接成功
[2025-08-21 10:54:01] 当前队列长度: 0
[2025-08-21 10:54:01] 当前队列长度: 0
[2025-08-21 10:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:54:02] 最终队列长度: 0
[2025-08-21 10:54:02] 最终队列长度: 0
[2025-08-21 10:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 10:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 10:57:01] ✅ Redis连接成功
[2025-08-21 10:57:01] ✅ Redis连接成功
[2025-08-21 10:57:01] 当前队列长度: 0
[2025-08-21 10:57:01] 当前队列长度: 0
[2025-08-21 10:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 10:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 10:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 10:57:01] 最终队列长度: 0
[2025-08-21 10:57:01] 最终队列长度: 0
[2025-08-21 10:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 10:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:00:02] === 自动入队脚本开始执行 ===
[2025-08-21 11:00:02] === 自动入队脚本开始执行 ===
[2025-08-21 11:00:02] ✅ Redis连接成功
[2025-08-21 11:00:02] ✅ Redis连接成功
[2025-08-21 11:00:02] 当前队列长度: 0
[2025-08-21 11:00:02] 当前队列长度: 0
[2025-08-21 11:00:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:00:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 11:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 11:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:00:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:00:02] 最终队列长度: 0
[2025-08-21 11:00:02] 最终队列长度: 0
[2025-08-21 11:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 11:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 11:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:03:01] ✅ Redis连接成功
[2025-08-21 11:03:01] ✅ Redis连接成功
[2025-08-21 11:03:01] 当前队列长度: 0
[2025-08-21 11:03:01] 当前队列长度: 0
[2025-08-21 11:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:03:01] 最终队列长度: 0
[2025-08-21 11:03:01] 最终队列长度: 0
[2025-08-21 11:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:06:01] ✅ Redis连接成功
[2025-08-21 11:06:01] ✅ Redis连接成功
[2025-08-21 11:06:01] 当前队列长度: 0
[2025-08-21 11:06:01] 当前队列长度: 0
[2025-08-21 11:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:06:01] 最终队列长度: 0
[2025-08-21 11:06:01] 最终队列长度: 0
[2025-08-21 11:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:09:01] ✅ Redis连接成功
[2025-08-21 11:09:01] ✅ Redis连接成功
[2025-08-21 11:09:01] 当前队列长度: 0
[2025-08-21 11:09:01] 当前队列长度: 0
[2025-08-21 11:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:09:01] 最终队列长度: 0
[2025-08-21 11:09:01] 最终队列长度: 0
[2025-08-21 11:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:12:01] ✅ Redis连接成功
[2025-08-21 11:12:01] ✅ Redis连接成功
[2025-08-21 11:12:01] 当前队列长度: 0
[2025-08-21 11:12:01] 当前队列长度: 0
[2025-08-21 11:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:12:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:12:01] 最终队列长度: 0
[2025-08-21 11:12:01] 最终队列长度: 0
[2025-08-21 11:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:15:01] ✅ Redis连接成功
[2025-08-21 11:15:01] ✅ Redis连接成功
[2025-08-21 11:15:01] 当前队列长度: 0
[2025-08-21 11:15:01] 当前队列长度: 0
[2025-08-21 11:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:15:01] 最终队列长度: 0
[2025-08-21 11:15:01] 最终队列长度: 0
[2025-08-21 11:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:18:01] ✅ Redis连接成功
[2025-08-21 11:18:01] ✅ Redis连接成功
[2025-08-21 11:18:01] 当前队列长度: 0
[2025-08-21 11:18:01] 当前队列长度: 0
[2025-08-21 11:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:18:01] 最终队列长度: 0
[2025-08-21 11:18:01] 最终队列长度: 0
[2025-08-21 11:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:21:01] ✅ Redis连接成功
[2025-08-21 11:21:01] ✅ Redis连接成功
[2025-08-21 11:21:01] 当前队列长度: 0
[2025-08-21 11:21:01] 当前队列长度: 0
[2025-08-21 11:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:21:01] 最终队列长度: 0
[2025-08-21 11:21:01] 最终队列长度: 0
[2025-08-21 11:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:24:01] ✅ Redis连接成功
[2025-08-21 11:24:01] ✅ Redis连接成功
[2025-08-21 11:24:01] 当前队列长度: 0
[2025-08-21 11:24:01] 当前队列长度: 0
[2025-08-21 11:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:24:01] 最终队列长度: 0
[2025-08-21 11:24:01] 最终队列长度: 0
[2025-08-21 11:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:27:01] ✅ Redis连接成功
[2025-08-21 11:27:01] ✅ Redis连接成功
[2025-08-21 11:27:01] 当前队列长度: 0
[2025-08-21 11:27:01] 当前队列长度: 0
[2025-08-21 11:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:27:01] 最终队列长度: 0
[2025-08-21 11:27:01] 最终队列长度: 0
[2025-08-21 11:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:30:01] ✅ Redis连接成功
[2025-08-21 11:30:01] ✅ Redis连接成功
[2025-08-21 11:30:01] 当前队列长度: 0
[2025-08-21 11:30:01] 当前队列长度: 0
[2025-08-21 11:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:30:01] 最终队列长度: 0
[2025-08-21 11:30:01] 最终队列长度: 0
[2025-08-21 11:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:33:01] ✅ Redis连接成功
[2025-08-21 11:33:01] ✅ Redis连接成功
[2025-08-21 11:33:01] 当前队列长度: 0
[2025-08-21 11:33:01] 当前队列长度: 0
[2025-08-21 11:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:33:01] 最终队列长度: 0
[2025-08-21 11:33:01] 最终队列长度: 0
[2025-08-21 11:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:36:01] ✅ Redis连接成功
[2025-08-21 11:36:01] ✅ Redis连接成功
[2025-08-21 11:36:01] 当前队列长度: 0
[2025-08-21 11:36:01] 当前队列长度: 0
[2025-08-21 11:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:36:01] 最终队列长度: 0
[2025-08-21 11:36:01] 最终队列长度: 0
[2025-08-21 11:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:39:01] ✅ Redis连接成功
[2025-08-21 11:39:01] ✅ Redis连接成功
[2025-08-21 11:39:01] 当前队列长度: 0
[2025-08-21 11:39:01] 当前队列长度: 0
[2025-08-21 11:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:39:01] 最终队列长度: 0
[2025-08-21 11:39:01] 最终队列长度: 0
[2025-08-21 11:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:42:02] === 自动入队脚本开始执行 ===
[2025-08-21 11:42:02] === 自动入队脚本开始执行 ===
[2025-08-21 11:42:02] ✅ Redis连接成功
[2025-08-21 11:42:02] ✅ Redis连接成功
[2025-08-21 11:42:02] 当前队列长度: 0
[2025-08-21 11:42:02] 当前队列长度: 0
[2025-08-21 11:42:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:42:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:42:02] ℹ️ 没有新订单需要入队
[2025-08-21 11:42:02] ℹ️ 没有新订单需要入队
[2025-08-21 11:42:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:42:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:42:02] 最终队列长度: 0
[2025-08-21 11:42:02] 最终队列长度: 0
[2025-08-21 11:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 11:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 11:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:45:01] ✅ Redis连接成功
[2025-08-21 11:45:01] ✅ Redis连接成功
[2025-08-21 11:45:01] 当前队列长度: 0
[2025-08-21 11:45:01] 当前队列长度: 0
[2025-08-21 11:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:45:01] 最终队列长度: 0
[2025-08-21 11:45:01] 最终队列长度: 0
[2025-08-21 11:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:48:01] ✅ Redis连接成功
[2025-08-21 11:48:01] ✅ Redis连接成功
[2025-08-21 11:48:01] 当前队列长度: 0
[2025-08-21 11:48:01] 当前队列长度: 0
[2025-08-21 11:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:48:01] 最终队列长度: 0
[2025-08-21 11:48:01] 最终队列长度: 0
[2025-08-21 11:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:51:01] ✅ Redis连接成功
[2025-08-21 11:51:01] ✅ Redis连接成功
[2025-08-21 11:51:01] 当前队列长度: 0
[2025-08-21 11:51:01] 当前队列长度: 0
[2025-08-21 11:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:51:01] 最终队列长度: 0
[2025-08-21 11:51:01] 最终队列长度: 0
[2025-08-21 11:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:54:01] ✅ Redis连接成功
[2025-08-21 11:54:01] ✅ Redis连接成功
[2025-08-21 11:54:01] 当前队列长度: 0
[2025-08-21 11:54:01] 当前队列长度: 0
[2025-08-21 11:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:54:02] 最终队列长度: 0
[2025-08-21 11:54:02] 最终队列长度: 0
[2025-08-21 11:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 11:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 11:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 11:57:01] ✅ Redis连接成功
[2025-08-21 11:57:01] ✅ Redis连接成功
[2025-08-21 11:57:01] 当前队列长度: 0
[2025-08-21 11:57:01] 当前队列长度: 0
[2025-08-21 11:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 11:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 11:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 11:57:01] 最终队列长度: 0
[2025-08-21 11:57:01] 最终队列长度: 0
[2025-08-21 11:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 11:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:00:01] ✅ Redis连接成功
[2025-08-21 12:00:01] ✅ Redis连接成功
[2025-08-21 12:00:01] 当前队列长度: 0
[2025-08-21 12:00:01] 当前队列长度: 0
[2025-08-21 12:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:00:01] 最终队列长度: 0
[2025-08-21 12:00:01] 最终队列长度: 0
[2025-08-21 12:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:03:01] ✅ Redis连接成功
[2025-08-21 12:03:01] ✅ Redis连接成功
[2025-08-21 12:03:01] 当前队列长度: 0
[2025-08-21 12:03:01] 当前队列长度: 0
[2025-08-21 12:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:03:01] 最终队列长度: 0
[2025-08-21 12:03:01] 最终队列长度: 0
[2025-08-21 12:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:06:01] ✅ Redis连接成功
[2025-08-21 12:06:01] ✅ Redis连接成功
[2025-08-21 12:06:01] 当前队列长度: 0
[2025-08-21 12:06:01] 当前队列长度: 0
[2025-08-21 12:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:06:01] 最终队列长度: 0
[2025-08-21 12:06:01] 最终队列长度: 0
[2025-08-21 12:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:09:02] === 自动入队脚本开始执行 ===
[2025-08-21 12:09:02] === 自动入队脚本开始执行 ===
[2025-08-21 12:09:02] ✅ Redis连接成功
[2025-08-21 12:09:02] ✅ Redis连接成功
[2025-08-21 12:09:02] 当前队列长度: 0
[2025-08-21 12:09:02] 当前队列长度: 0
[2025-08-21 12:09:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:09:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:09:02] ℹ️ 没有新订单需要入队
[2025-08-21 12:09:02] ℹ️ 没有新订单需要入队
[2025-08-21 12:09:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:09:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:09:02] 最终队列长度: 0
[2025-08-21 12:09:02] 最终队列长度: 0
[2025-08-21 12:09:02] === 自动入队脚本执行完成 ===
[2025-08-21 12:09:02] === 自动入队脚本执行完成 ===
[2025-08-21 12:12:02] === 自动入队脚本开始执行 ===
[2025-08-21 12:12:02] === 自动入队脚本开始执行 ===
[2025-08-21 12:12:02] ✅ Redis连接成功
[2025-08-21 12:12:02] ✅ Redis连接成功
[2025-08-21 12:12:02] 当前队列长度: 0
[2025-08-21 12:12:02] 当前队列长度: 0
[2025-08-21 12:12:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:12:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:12:02] ℹ️ 没有新订单需要入队
[2025-08-21 12:12:02] ℹ️ 没有新订单需要入队
[2025-08-21 12:12:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:12:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:12:02] 最终队列长度: 0
[2025-08-21 12:12:02] 最终队列长度: 0
[2025-08-21 12:12:02] === 自动入队脚本执行完成 ===
[2025-08-21 12:12:02] === 自动入队脚本执行完成 ===
[2025-08-21 12:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:15:01] ✅ Redis连接成功
[2025-08-21 12:15:01] ✅ Redis连接成功
[2025-08-21 12:15:01] 当前队列长度: 0
[2025-08-21 12:15:01] 当前队列长度: 0
[2025-08-21 12:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:15:01] 最终队列长度: 0
[2025-08-21 12:15:01] 最终队列长度: 0
[2025-08-21 12:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:18:01] ✅ Redis连接成功
[2025-08-21 12:18:01] ✅ Redis连接成功
[2025-08-21 12:18:01] 当前队列长度: 0
[2025-08-21 12:18:01] 当前队列长度: 0
[2025-08-21 12:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:18:01] 最终队列长度: 0
[2025-08-21 12:18:01] 最终队列长度: 0
[2025-08-21 12:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:21:01] ✅ Redis连接成功
[2025-08-21 12:21:01] ✅ Redis连接成功
[2025-08-21 12:21:01] 当前队列长度: 0
[2025-08-21 12:21:01] 当前队列长度: 0
[2025-08-21 12:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:21:01] 最终队列长度: 0
[2025-08-21 12:21:01] 最终队列长度: 0
[2025-08-21 12:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:24:01] ✅ Redis连接成功
[2025-08-21 12:24:01] ✅ Redis连接成功
[2025-08-21 12:24:01] 当前队列长度: 0
[2025-08-21 12:24:01] 当前队列长度: 0
[2025-08-21 12:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:24:01] 最终队列长度: 0
[2025-08-21 12:24:01] 最终队列长度: 0
[2025-08-21 12:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:27:01] ✅ Redis连接成功
[2025-08-21 12:27:01] ✅ Redis连接成功
[2025-08-21 12:27:01] 当前队列长度: 0
[2025-08-21 12:27:01] 当前队列长度: 0
[2025-08-21 12:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:27:01] 最终队列长度: 0
[2025-08-21 12:27:01] 最终队列长度: 0
[2025-08-21 12:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:30:01] ✅ Redis连接成功
[2025-08-21 12:30:01] ✅ Redis连接成功
[2025-08-21 12:30:01] 当前队列长度: 0
[2025-08-21 12:30:01] 当前队列长度: 0
[2025-08-21 12:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:30:01] 最终队列长度: 0
[2025-08-21 12:30:01] 最终队列长度: 0
[2025-08-21 12:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:33:01] ✅ Redis连接成功
[2025-08-21 12:33:01] ✅ Redis连接成功
[2025-08-21 12:33:01] 当前队列长度: 0
[2025-08-21 12:33:01] 当前队列长度: 0
[2025-08-21 12:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:33:01] 最终队列长度: 0
[2025-08-21 12:33:01] 最终队列长度: 0
[2025-08-21 12:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:36:01] ✅ Redis连接成功
[2025-08-21 12:36:01] ✅ Redis连接成功
[2025-08-21 12:36:01] 当前队列长度: 0
[2025-08-21 12:36:01] 当前队列长度: 0
[2025-08-21 12:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:36:01] 最终队列长度: 0
[2025-08-21 12:36:01] 最终队列长度: 0
[2025-08-21 12:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:39:01] ✅ Redis连接成功
[2025-08-21 12:39:01] ✅ Redis连接成功
[2025-08-21 12:39:01] 当前队列长度: 0
[2025-08-21 12:39:01] 当前队列长度: 0
[2025-08-21 12:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:39:01] 最终队列长度: 0
[2025-08-21 12:39:01] 最终队列长度: 0
[2025-08-21 12:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:42:01] ✅ Redis连接成功
[2025-08-21 12:42:01] ✅ Redis连接成功
[2025-08-21 12:42:01] 当前队列长度: 0
[2025-08-21 12:42:01] 当前队列长度: 0
[2025-08-21 12:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:42:01] 最终队列长度: 0
[2025-08-21 12:42:01] 最终队列长度: 0
[2025-08-21 12:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:45:01] ✅ Redis连接成功
[2025-08-21 12:45:01] ✅ Redis连接成功
[2025-08-21 12:45:01] 当前队列长度: 0
[2025-08-21 12:45:01] 当前队列长度: 0
[2025-08-21 12:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:45:01] 最终队列长度: 0
[2025-08-21 12:45:01] 最终队列长度: 0
[2025-08-21 12:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:48:01] ✅ Redis连接成功
[2025-08-21 12:48:01] ✅ Redis连接成功
[2025-08-21 12:48:01] 当前队列长度: 0
[2025-08-21 12:48:01] 当前队列长度: 0
[2025-08-21 12:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:48:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:48:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:48:02] 最终队列长度: 0
[2025-08-21 12:48:02] 最终队列长度: 0
[2025-08-21 12:48:02] === 自动入队脚本执行完成 ===
[2025-08-21 12:48:02] === 自动入队脚本执行完成 ===
[2025-08-21 12:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:51:01] ✅ Redis连接成功
[2025-08-21 12:51:01] ✅ Redis连接成功
[2025-08-21 12:51:01] 当前队列长度: 0
[2025-08-21 12:51:01] 当前队列长度: 0
[2025-08-21 12:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:51:01] 最终队列长度: 0
[2025-08-21 12:51:01] 最终队列长度: 0
[2025-08-21 12:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:54:01] ✅ Redis连接成功
[2025-08-21 12:54:01] ✅ Redis连接成功
[2025-08-21 12:54:01] 当前队列长度: 0
[2025-08-21 12:54:01] 当前队列长度: 0
[2025-08-21 12:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:54:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:54:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:54:01] 最终队列长度: 0
[2025-08-21 12:54:01] 最终队列长度: 0
[2025-08-21 12:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 12:57:01] ✅ Redis连接成功
[2025-08-21 12:57:01] ✅ Redis连接成功
[2025-08-21 12:57:01] 当前队列长度: 0
[2025-08-21 12:57:01] 当前队列长度: 0
[2025-08-21 12:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 12:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 12:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 12:57:01] 最终队列长度: 0
[2025-08-21 12:57:01] 最终队列长度: 0
[2025-08-21 12:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 12:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:00:01] ✅ Redis连接成功
[2025-08-21 13:00:01] ✅ Redis连接成功
[2025-08-21 13:00:01] 当前队列长度: 0
[2025-08-21 13:00:01] 当前队列长度: 0
[2025-08-21 13:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:00:01] 最终队列长度: 0
[2025-08-21 13:00:01] 最终队列长度: 0
[2025-08-21 13:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:03:01] ✅ Redis连接成功
[2025-08-21 13:03:01] ✅ Redis连接成功
[2025-08-21 13:03:01] 当前队列长度: 0
[2025-08-21 13:03:01] 当前队列长度: 0
[2025-08-21 13:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:03:01] 最终队列长度: 0
[2025-08-21 13:03:01] 最终队列长度: 0
[2025-08-21 13:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:06:01] ✅ Redis连接成功
[2025-08-21 13:06:01] ✅ Redis连接成功
[2025-08-21 13:06:01] 当前队列长度: 0
[2025-08-21 13:06:01] 当前队列长度: 0
[2025-08-21 13:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:06:01] 最终队列长度: 0
[2025-08-21 13:06:01] 最终队列长度: 0
[2025-08-21 13:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:09:01] ✅ Redis连接成功
[2025-08-21 13:09:01] ✅ Redis连接成功
[2025-08-21 13:09:01] 当前队列长度: 0
[2025-08-21 13:09:01] 当前队列长度: 0
[2025-08-21 13:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:09:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:09:01] 最终队列长度: 0
[2025-08-21 13:09:01] 最终队列长度: 0
[2025-08-21 13:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:12:01] ✅ Redis连接成功
[2025-08-21 13:12:01] ✅ Redis连接成功
[2025-08-21 13:12:01] 当前队列长度: 0
[2025-08-21 13:12:01] 当前队列长度: 0
[2025-08-21 13:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:12:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:12:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:12:02] 最终队列长度: 0
[2025-08-21 13:12:02] 最终队列长度: 0
[2025-08-21 13:12:02] === 自动入队脚本执行完成 ===
[2025-08-21 13:12:02] === 自动入队脚本执行完成 ===
[2025-08-21 13:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:15:01] ✅ Redis连接成功
[2025-08-21 13:15:01] ✅ Redis连接成功
[2025-08-21 13:15:01] 当前队列长度: 0
[2025-08-21 13:15:01] 当前队列长度: 0
[2025-08-21 13:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:15:01] 最终队列长度: 0
[2025-08-21 13:15:01] 最终队列长度: 0
[2025-08-21 13:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:18:01] ✅ Redis连接成功
[2025-08-21 13:18:01] ✅ Redis连接成功
[2025-08-21 13:18:01] 当前队列长度: 0
[2025-08-21 13:18:01] 当前队列长度: 0
[2025-08-21 13:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:18:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:18:01] 最终队列长度: 0
[2025-08-21 13:18:01] 最终队列长度: 0
[2025-08-21 13:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:21:01] ✅ Redis连接成功
[2025-08-21 13:21:01] ✅ Redis连接成功
[2025-08-21 13:21:01] 当前队列长度: 0
[2025-08-21 13:21:01] 当前队列长度: 0
[2025-08-21 13:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:21:01] 最终队列长度: 0
[2025-08-21 13:21:01] 最终队列长度: 0
[2025-08-21 13:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:24:01] ✅ Redis连接成功
[2025-08-21 13:24:01] ✅ Redis连接成功
[2025-08-21 13:24:01] 当前队列长度: 0
[2025-08-21 13:24:01] 当前队列长度: 0
[2025-08-21 13:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:24:01] 最终队列长度: 0
[2025-08-21 13:24:01] 最终队列长度: 0
[2025-08-21 13:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:27:01] ✅ Redis连接成功
[2025-08-21 13:27:01] ✅ Redis连接成功
[2025-08-21 13:27:01] 当前队列长度: 0
[2025-08-21 13:27:01] 当前队列长度: 0
[2025-08-21 13:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:27:01] 最终队列长度: 0
[2025-08-21 13:27:01] 最终队列长度: 0
[2025-08-21 13:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:30:01] ✅ Redis连接成功
[2025-08-21 13:30:01] ✅ Redis连接成功
[2025-08-21 13:30:01] 当前队列长度: 0
[2025-08-21 13:30:01] 当前队列长度: 0
[2025-08-21 13:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:30:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:30:02] 最终队列长度: 0
[2025-08-21 13:30:02] 最终队列长度: 0
[2025-08-21 13:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 13:30:02] === 自动入队脚本执行完成 ===
[2025-08-21 13:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:33:01] ✅ Redis连接成功
[2025-08-21 13:33:01] ✅ Redis连接成功
[2025-08-21 13:33:01] 当前队列长度: 0
[2025-08-21 13:33:01] 当前队列长度: 0
[2025-08-21 13:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:33:01] 最终队列长度: 0
[2025-08-21 13:33:01] 最终队列长度: 0
[2025-08-21 13:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:36:01] ✅ Redis连接成功
[2025-08-21 13:36:01] ✅ Redis连接成功
[2025-08-21 13:36:01] 当前队列长度: 0
[2025-08-21 13:36:01] 当前队列长度: 0
[2025-08-21 13:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:36:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:36:01] 最终队列长度: 0
[2025-08-21 13:36:01] 最终队列长度: 0
[2025-08-21 13:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:39:01] ✅ Redis连接成功
[2025-08-21 13:39:01] ✅ Redis连接成功
[2025-08-21 13:39:01] 当前队列长度: 0
[2025-08-21 13:39:01] 当前队列长度: 0
[2025-08-21 13:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:39:01] 最终队列长度: 0
[2025-08-21 13:39:01] 最终队列长度: 0
[2025-08-21 13:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:42:01] ✅ Redis连接成功
[2025-08-21 13:42:01] ✅ Redis连接成功
[2025-08-21 13:42:01] 当前队列长度: 0
[2025-08-21 13:42:01] 当前队列长度: 0
[2025-08-21 13:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:42:01] 最终队列长度: 0
[2025-08-21 13:42:01] 最终队列长度: 0
[2025-08-21 13:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:45:01] ✅ Redis连接成功
[2025-08-21 13:45:01] ✅ Redis连接成功
[2025-08-21 13:45:01] 当前队列长度: 0
[2025-08-21 13:45:01] 当前队列长度: 0
[2025-08-21 13:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:45:01] 最终队列长度: 0
[2025-08-21 13:45:01] 最终队列长度: 0
[2025-08-21 13:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:48:01] ✅ Redis连接成功
[2025-08-21 13:48:01] ✅ Redis连接成功
[2025-08-21 13:48:01] 当前队列长度: 0
[2025-08-21 13:48:01] 当前队列长度: 0
[2025-08-21 13:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:48:01] 最终队列长度: 0
[2025-08-21 13:48:01] 最终队列长度: 0
[2025-08-21 13:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:51:01] ✅ Redis连接成功
[2025-08-21 13:51:01] ✅ Redis连接成功
[2025-08-21 13:51:01] 当前队列长度: 0
[2025-08-21 13:51:01] 当前队列长度: 0
[2025-08-21 13:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:51:01] 最终队列长度: 0
[2025-08-21 13:51:01] 最终队列长度: 0
[2025-08-21 13:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:54:02] === 自动入队脚本开始执行 ===
[2025-08-21 13:54:02] === 自动入队脚本开始执行 ===
[2025-08-21 13:54:02] ✅ Redis连接成功
[2025-08-21 13:54:02] ✅ Redis连接成功
[2025-08-21 13:54:02] 当前队列长度: 0
[2025-08-21 13:54:02] 当前队列长度: 0
[2025-08-21 13:54:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:54:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:54:02] ℹ️ 没有新订单需要入队
[2025-08-21 13:54:02] ℹ️ 没有新订单需要入队
[2025-08-21 13:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:54:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:54:02] 最终队列长度: 0
[2025-08-21 13:54:02] 最终队列长度: 0
[2025-08-21 13:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 13:54:02] === 自动入队脚本执行完成 ===
[2025-08-21 13:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 13:57:01] ✅ Redis连接成功
[2025-08-21 13:57:01] ✅ Redis连接成功
[2025-08-21 13:57:01] 当前队列长度: 0
[2025-08-21 13:57:01] 当前队列长度: 0
[2025-08-21 13:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 13:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 13:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:57:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 13:57:01] 最终队列长度: 0
[2025-08-21 13:57:01] 最终队列长度: 0
[2025-08-21 13:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 13:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:00:01] ✅ Redis连接成功
[2025-08-21 14:00:01] ✅ Redis连接成功
[2025-08-21 14:00:01] 当前队列长度: 0
[2025-08-21 14:00:01] 当前队列长度: 0
[2025-08-21 14:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:00:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:00:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:00:01] 最终队列长度: 0
[2025-08-21 14:00:01] 最终队列长度: 0
[2025-08-21 14:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:03:01] ✅ Redis连接成功
[2025-08-21 14:03:01] ✅ Redis连接成功
[2025-08-21 14:03:01] 当前队列长度: 0
[2025-08-21 14:03:01] 当前队列长度: 0
[2025-08-21 14:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:03:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:03:01] 最终队列长度: 0
[2025-08-21 14:03:01] 最终队列长度: 0
[2025-08-21 14:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:06:01] ✅ Redis连接成功
[2025-08-21 14:06:01] ✅ Redis连接成功
[2025-08-21 14:06:01] 当前队列长度: 0
[2025-08-21 14:06:01] 当前队列长度: 0
[2025-08-21 14:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:06:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:06:01] 最终队列长度: 0
[2025-08-21 14:06:01] 最终队列长度: 0
[2025-08-21 14:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:09:02] === 自动入队脚本开始执行 ===
[2025-08-21 14:09:02] === 自动入队脚本开始执行 ===
[2025-08-21 14:09:02] ✅ Redis连接成功
[2025-08-21 14:09:02] ✅ Redis连接成功
[2025-08-21 14:09:02] 当前队列长度: 0
[2025-08-21 14:09:02] 当前队列长度: 0
[2025-08-21 14:09:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:09:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:09:02] ℹ️ 没有新订单需要入队
[2025-08-21 14:09:02] ℹ️ 没有新订单需要入队
[2025-08-21 14:09:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:09:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:09:02] 最终队列长度: 0
[2025-08-21 14:09:02] 最终队列长度: 0
[2025-08-21 14:09:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:09:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:12:01] ✅ Redis连接成功
[2025-08-21 14:12:01] ✅ Redis连接成功
[2025-08-21 14:12:01] 当前队列长度: 0
[2025-08-21 14:12:01] 当前队列长度: 0
[2025-08-21 14:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:12:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:12:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:12:02] 最终队列长度: 0
[2025-08-21 14:12:02] 最终队列长度: 0
[2025-08-21 14:12:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:12:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:15:01] ✅ Redis连接成功
[2025-08-21 14:15:01] ✅ Redis连接成功
[2025-08-21 14:15:01] 当前队列长度: 0
[2025-08-21 14:15:01] 当前队列长度: 0
[2025-08-21 14:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:15:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:15:01] 最终队列长度: 0
[2025-08-21 14:15:01] 最终队列长度: 0
[2025-08-21 14:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:18:02] === 自动入队脚本开始执行 ===
[2025-08-21 14:18:02] === 自动入队脚本开始执行 ===
[2025-08-21 14:18:02] ✅ Redis连接成功
[2025-08-21 14:18:02] ✅ Redis连接成功
[2025-08-21 14:18:02] 当前队列长度: 0
[2025-08-21 14:18:02] 当前队列长度: 0
[2025-08-21 14:18:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:18:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:18:02] ℹ️ 没有新订单需要入队
[2025-08-21 14:18:02] ℹ️ 没有新订单需要入队
[2025-08-21 14:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:18:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:18:02] 最终队列长度: 0
[2025-08-21 14:18:02] 最终队列长度: 0
[2025-08-21 14:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:18:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:21:01] ✅ Redis连接成功
[2025-08-21 14:21:01] ✅ Redis连接成功
[2025-08-21 14:21:01] 当前队列长度: 0
[2025-08-21 14:21:01] 当前队列长度: 0
[2025-08-21 14:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:21:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:21:01] 最终队列长度: 0
[2025-08-21 14:21:01] 最终队列长度: 0
[2025-08-21 14:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:21:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:24:01] ✅ Redis连接成功
[2025-08-21 14:24:01] ✅ Redis连接成功
[2025-08-21 14:24:01] 当前队列长度: 0
[2025-08-21 14:24:01] 当前队列长度: 0
[2025-08-21 14:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:24:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:24:01] 最终队列长度: 0
[2025-08-21 14:24:01] 最终队列长度: 0
[2025-08-21 14:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:24:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:27:01] ✅ Redis连接成功
[2025-08-21 14:27:01] ✅ Redis连接成功
[2025-08-21 14:27:01] 当前队列长度: 0
[2025-08-21 14:27:01] 当前队列长度: 0
[2025-08-21 14:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:27:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:27:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:27:01] 最终队列长度: 0
[2025-08-21 14:27:01] 最终队列长度: 0
[2025-08-21 14:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:30:01] ✅ Redis连接成功
[2025-08-21 14:30:01] ✅ Redis连接成功
[2025-08-21 14:30:01] 当前队列长度: 0
[2025-08-21 14:30:01] 当前队列长度: 0
[2025-08-21 14:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:30:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:30:01] 最终队列长度: 0
[2025-08-21 14:30:01] 最终队列长度: 0
[2025-08-21 14:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:33:01] ✅ Redis连接成功
[2025-08-21 14:33:01] ✅ Redis连接成功
[2025-08-21 14:33:01] 当前队列长度: 0
[2025-08-21 14:33:01] 当前队列长度: 0
[2025-08-21 14:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:33:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:33:01] 最终队列长度: 0
[2025-08-21 14:33:01] 最终队列长度: 0
[2025-08-21 14:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:36:02] === 自动入队脚本开始执行 ===
[2025-08-21 14:36:02] === 自动入队脚本开始执行 ===
[2025-08-21 14:36:02] ✅ Redis连接成功
[2025-08-21 14:36:02] ✅ Redis连接成功
[2025-08-21 14:36:02] 当前队列长度: 0
[2025-08-21 14:36:02] 当前队列长度: 0
[2025-08-21 14:36:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:36:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 14:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 14:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:36:02] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:36:02] 最终队列长度: 0
[2025-08-21 14:36:02] 最终队列长度: 0
[2025-08-21 14:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:36:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:39:01] ✅ Redis连接成功
[2025-08-21 14:39:01] ✅ Redis连接成功
[2025-08-21 14:39:01] 当前队列长度: 0
[2025-08-21 14:39:01] 当前队列长度: 0
[2025-08-21 14:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:39:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:39:01] 最终队列长度: 0
[2025-08-21 14:39:01] 最终队列长度: 0
[2025-08-21 14:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:42:01] ✅ Redis连接成功
[2025-08-21 14:42:01] ✅ Redis连接成功
[2025-08-21 14:42:01] 当前队列长度: 0
[2025-08-21 14:42:01] 当前队列长度: 0
[2025-08-21 14:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:42:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:42:01] 最终队列长度: 0
[2025-08-21 14:42:01] 最终队列长度: 0
[2025-08-21 14:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:42:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:45:01] ✅ Redis连接成功
[2025-08-21 14:45:01] ✅ Redis连接成功
[2025-08-21 14:45:01] 当前队列长度: 0
[2025-08-21 14:45:01] 当前队列长度: 0
[2025-08-21 14:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:45:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:45:01] 最终队列长度: 0
[2025-08-21 14:45:01] 最终队列长度: 0
[2025-08-21 14:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:48:01] ✅ Redis连接成功
[2025-08-21 14:48:01] ✅ Redis连接成功
[2025-08-21 14:48:01] 当前队列长度: 0
[2025-08-21 14:48:01] 当前队列长度: 0
[2025-08-21 14:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:48:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:48:01] 最终队列长度: 0
[2025-08-21 14:48:01] 最终队列长度: 0
[2025-08-21 14:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:51:01] ✅ Redis连接成功
[2025-08-21 14:51:01] ✅ Redis连接成功
[2025-08-21 14:51:01] 当前队列长度: 0
[2025-08-21 14:51:01] 当前队列长度: 0
[2025-08-21 14:51:01] 准备入队: 订单47 - 13656580928 - 运行中 - 获取失败
[2025-08-21 14:51:01] 准备入队: 订单47 - 13656580928 - 运行中 - 获取失败
[2025-08-21 14:51:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 14:51:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 14:51:01] ✅ 成功入队1个订单
[2025-08-21 14:51:01] ✅ 成功入队1个订单
[2025-08-21 14:51:01] 更新1个订单状态为'待更新'
[2025-08-21 14:51:01] 更新1个订单状态为'待更新'
[2025-08-21 14:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:51:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:51:01] 最终队列长度: 1
[2025-08-21 14:51:01] 最终队列长度: 1
[2025-08-21 14:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:54:01] ✅ Redis连接成功
[2025-08-21 14:54:01] ✅ Redis连接成功
[2025-08-21 14:54:01] 当前队列长度: 0
[2025-08-21 14:54:01] 当前队列长度: 0
[2025-08-21 14:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 14:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 14:54:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:54:01] 守护进程状态: 3/{count(Array)} 个进程在运行
[2025-08-21 14:54:01] 最终队列长度: 0
[2025-08-21 14:54:01] 最终队列长度: 0
[2025-08-21 14:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 14:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 14:57:01] ✅ Redis连接成功
[2025-08-21 14:57:01] ✅ Redis连接成功
[2025-08-21 14:57:01] 当前队列长度: 0
[2025-08-21 14:57:01] 当前队列长度: 0
[2025-08-21 14:57:01] 准备入队: 订单44 - 18723589157 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单44 - 18723589157 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单45 - 15171068131 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单45 - 15171068131 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单46 - 13957632570 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单46 - 13957632570 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单47 - 13656580928 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 准备入队: 订单47 - 13656580928 - 运行中 - chusheng-获取失败
[2025-08-21 14:57:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 14:57:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 14:57:01] ✅ 成功入队4个订单
[2025-08-21 14:57:01] ✅ 成功入队4个订单
[2025-08-21 14:57:01] 更新4个订单状态为'待更新'
[2025-08-21 14:57:01] 更新4个订单状态为'待更新'
[2025-08-21 14:57:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 14:57:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 14:57:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 14:57:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 14:57:02] 最终队列长度: 4
[2025-08-21 14:57:02] 最终队列长度: 4
[2025-08-21 14:57:02] === 自动入队脚本执行完成 ===
[2025-08-21 14:57:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:00:02] === 自动入队脚本开始执行 ===
[2025-08-21 15:00:02] === 自动入队脚本开始执行 ===
[2025-08-21 15:00:02] ✅ Redis连接成功
[2025-08-21 15:00:02] ✅ Redis连接成功
[2025-08-21 15:00:02] 当前队列长度: 0
[2025-08-21 15:00:02] 当前队列长度: 0
[2025-08-21 15:00:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:00:02] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 15:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 15:00:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:00:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:00:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:00:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:00:02] 最终队列长度: 0
[2025-08-21 15:00:02] 最终队列长度: 0
[2025-08-21 15:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:00:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:03:01] ✅ Redis连接成功
[2025-08-21 15:03:01] ✅ Redis连接成功
[2025-08-21 15:03:01] 当前队列长度: 0
[2025-08-21 15:03:01] 当前队列长度: 0
[2025-08-21 15:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:03:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:03:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:03:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:03:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:03:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:03:01] 最终队列长度: 0
[2025-08-21 15:03:01] 最终队列长度: 0
[2025-08-21 15:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:06:01] ✅ Redis连接成功
[2025-08-21 15:06:01] ✅ Redis连接成功
[2025-08-21 15:06:01] 当前队列长度: 0
[2025-08-21 15:06:01] 当前队列长度: 0
[2025-08-21 15:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:06:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:06:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:06:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:06:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:06:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:06:01] 最终队列长度: 0
[2025-08-21 15:06:01] 最终队列长度: 0
[2025-08-21 15:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:06:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:09:01] ✅ Redis连接成功
[2025-08-21 15:09:01] ✅ Redis连接成功
[2025-08-21 15:09:01] 当前队列长度: 4
[2025-08-21 15:09:01] 当前队列长度: 4
[2025-08-21 15:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:09:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:09:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:09:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:09:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:09:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:09:01] 最终队列长度: 4
[2025-08-21 15:09:01] 最终队列长度: 4
[2025-08-21 15:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:12:01] ✅ Redis连接成功
[2025-08-21 15:12:01] ✅ Redis连接成功
[2025-08-21 15:12:01] 当前队列长度: 4
[2025-08-21 15:12:01] 当前队列长度: 4
[2025-08-21 15:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:12:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:12:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:12:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:12:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:12:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:12:01] 最终队列长度: 4
[2025-08-21 15:12:01] 最终队列长度: 4
[2025-08-21 15:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:15:01] ✅ Redis连接成功
[2025-08-21 15:15:01] ✅ Redis连接成功
[2025-08-21 15:15:01] 当前队列长度: 8
[2025-08-21 15:15:01] 当前队列长度: 8
[2025-08-21 15:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:15:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:15:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:15:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:15:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:15:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:15:01] 最终队列长度: 8
[2025-08-21 15:15:01] 最终队列长度: 8
[2025-08-21 15:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:18:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:18:01] ✅ Redis连接成功
[2025-08-21 15:18:01] ✅ Redis连接成功
[2025-08-21 15:18:01] 当前队列长度: 8
[2025-08-21 15:18:01] 当前队列长度: 8
[2025-08-21 15:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:18:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:18:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:18:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:18:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:18:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:18:01] 最终队列长度: 8
[2025-08-21 15:18:01] 最终队列长度: 8
[2025-08-21 15:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:18:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:21:01] ✅ Redis连接成功
[2025-08-21 15:21:01] ✅ Redis连接成功
[2025-08-21 15:21:01] 当前队列长度: 8
[2025-08-21 15:21:01] 当前队列长度: 8
[2025-08-21 15:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:21:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:21:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:21:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:21:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:21:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:21:02] 最终队列长度: 8
[2025-08-21 15:21:02] 最终队列长度: 8
[2025-08-21 15:21:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:21:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:24:01] ✅ Redis连接成功
[2025-08-21 15:24:01] ✅ Redis连接成功
[2025-08-21 15:24:01] 当前队列长度: 8
[2025-08-21 15:24:01] 当前队列长度: 8
[2025-08-21 15:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:24:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:24:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:24:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:24:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:24:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:24:02] 最终队列长度: 8
[2025-08-21 15:24:02] 最终队列长度: 8
[2025-08-21 15:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:27:01] ✅ Redis连接成功
[2025-08-21 15:27:01] ✅ Redis连接成功
[2025-08-21 15:27:01] 当前队列长度: 8
[2025-08-21 15:27:01] 当前队列长度: 8
[2025-08-21 15:27:01] 准备入队: 订单48 - 13649324102 - 上号中 - 
[2025-08-21 15:27:01] 准备入队: 订单48 - 13649324102 - 上号中 - 
[2025-08-21 15:27:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 15:27:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 15:27:01] ✅ 成功入队1个订单
[2025-08-21 15:27:01] ✅ 成功入队1个订单
[2025-08-21 15:27:01] 更新1个订单状态为'待更新'
[2025-08-21 15:27:01] 更新1个订单状态为'待更新'
[2025-08-21 15:27:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:27:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:27:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:27:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:27:01] 最终队列长度: 9
[2025-08-21 15:27:01] 最终队列长度: 9
[2025-08-21 15:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:30:01] ✅ Redis连接成功
[2025-08-21 15:30:01] ✅ Redis连接成功
[2025-08-21 15:30:01] 当前队列长度: 9
[2025-08-21 15:30:01] 当前队列长度: 9
[2025-08-21 15:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:30:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:30:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:30:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:30:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:30:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:30:01] 最终队列长度: 9
[2025-08-21 15:30:01] 最终队列长度: 9
[2025-08-21 15:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:33:01] ✅ Redis连接成功
[2025-08-21 15:33:01] ✅ Redis连接成功
[2025-08-21 15:33:01] 当前队列长度: 9
[2025-08-21 15:33:01] 当前队列长度: 9
[2025-08-21 15:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:33:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:33:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:33:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:33:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:33:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:33:01] 最终队列长度: 9
[2025-08-21 15:33:01] 最终队列长度: 9
[2025-08-21 15:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:33:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:36:01] ✅ Redis连接成功
[2025-08-21 15:36:01] ✅ Redis连接成功
[2025-08-21 15:36:01] 当前队列长度: 9
[2025-08-21 15:36:01] 当前队列长度: 9
[2025-08-21 15:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:36:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:36:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:36:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:36:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:36:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:36:01] 最终队列长度: 9
[2025-08-21 15:36:01] 最终队列长度: 9
[2025-08-21 15:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:39:01] ✅ Redis连接成功
[2025-08-21 15:39:01] ✅ Redis连接成功
[2025-08-21 15:39:01] 当前队列长度: 9
[2025-08-21 15:39:01] 当前队列长度: 9
[2025-08-21 15:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:39:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:39:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:39:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:39:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:39:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:39:01] 最终队列长度: 9
[2025-08-21 15:39:01] 最终队列长度: 9
[2025-08-21 15:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:42:01] ✅ Redis连接成功
[2025-08-21 15:42:01] ✅ Redis连接成功
[2025-08-21 15:42:01] 当前队列长度: 9
[2025-08-21 15:42:01] 当前队列长度: 9
[2025-08-21 15:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:42:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:42:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:42:02] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:42:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:42:02] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:42:02] 最终队列长度: 9
[2025-08-21 15:42:02] 最终队列长度: 9
[2025-08-21 15:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 15:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:45:01] ✅ Redis连接成功
[2025-08-21 15:45:01] ✅ Redis连接成功
[2025-08-21 15:45:01] 当前队列长度: 9
[2025-08-21 15:45:01] 当前队列长度: 9
[2025-08-21 15:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:45:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:45:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:45:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:45:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:45:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:45:01] 最终队列长度: 9
[2025-08-21 15:45:01] 最终队列长度: 9
[2025-08-21 15:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:48:01] ✅ Redis连接成功
[2025-08-21 15:48:01] ✅ Redis连接成功
[2025-08-21 15:48:01] 当前队列长度: 9
[2025-08-21 15:48:01] 当前队列长度: 9
[2025-08-21 15:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:48:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:48:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:48:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:48:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:48:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:48:01] 最终队列长度: 9
[2025-08-21 15:48:01] 最终队列长度: 9
[2025-08-21 15:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:48:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:51:01] ✅ Redis连接成功
[2025-08-21 15:51:01] ✅ Redis连接成功
[2025-08-21 15:51:01] 当前队列长度: 9
[2025-08-21 15:51:01] 当前队列长度: 9
[2025-08-21 15:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:51:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:51:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:51:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:51:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:51:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:51:01] 最终队列长度: 9
[2025-08-21 15:51:01] 最终队列长度: 9
[2025-08-21 15:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:54:01] ✅ Redis连接成功
[2025-08-21 15:54:01] ✅ Redis连接成功
[2025-08-21 15:54:01] 当前队列长度: 9
[2025-08-21 15:54:01] 当前队列长度: 9
[2025-08-21 15:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:54:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:54:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:54:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:54:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:54:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:54:01] 最终队列长度: 9
[2025-08-21 15:54:01] 最终队列长度: 9
[2025-08-21 15:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 15:57:01] ✅ Redis连接成功
[2025-08-21 15:57:01] ✅ Redis连接成功
[2025-08-21 15:57:01] 当前队列长度: 9
[2025-08-21 15:57:01] 当前队列长度: 9
[2025-08-21 15:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:57:01] 扫描到0个订单，其中{count(Array)}个需要入队
[2025-08-21 15:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 15:57:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:57:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 15:57:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:57:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 15:57:01] 最终队列长度: 9
[2025-08-21 15:57:01] 最终队列长度: 9
[2025-08-21 15:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 15:57:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:00:01] ✅ Redis连接成功
[2025-08-21 16:00:01] ✅ Redis连接成功
[2025-08-21 16:00:01] 当前队列长度: 0
[2025-08-21 16:00:01] 当前队列长度: 0
[2025-08-21 16:00:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:00:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:00:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:00:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:00:01] ✅ 成功入队1个订单
[2025-08-21 16:00:01] ✅ 成功入队1个订单
[2025-08-21 16:00:01] 更新1个订单状态为'待更新'
[2025-08-21 16:00:01] 更新1个订单状态为'待更新'
[2025-08-21 16:00:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 16:00:01] 守护进程状态: 0/{count(Array)} 个进程在运行
[2025-08-21 16:00:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 16:00:01] ⚠️ 警告: 没有守护进程在运行！
[2025-08-21 16:00:01] 最终队列长度: 1
[2025-08-21 16:00:01] 最终队列长度: 1
[2025-08-21 16:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:03:01] ✅ Redis连接成功
[2025-08-21 16:03:01] ✅ Redis连接成功
[2025-08-21 16:03:01] 当前队列长度: 0
[2025-08-21 16:03:01] 当前队列长度: 0
[2025-08-21 16:03:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:03:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:03:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:03:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:03:01] ✅ 成功入队1个订单
[2025-08-21 16:03:01] ✅ 成功入队1个订单
[2025-08-21 16:03:01] 更新1个订单状态为'待更新'
[2025-08-21 16:03:01] 更新1个订单状态为'待更新'
[2025-08-21 16:03:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:03:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:03:01] 最终队列长度: 1
[2025-08-21 16:03:01] 最终队列长度: 1
[2025-08-21 16:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:06:02] === 自动入队脚本开始执行 ===
[2025-08-21 16:06:02] === 自动入队脚本开始执行 ===
[2025-08-21 16:06:02] ✅ Redis连接成功
[2025-08-21 16:06:02] ✅ Redis连接成功
[2025-08-21 16:06:02] 当前队列长度: 0
[2025-08-21 16:06:02] 当前队列长度: 0
[2025-08-21 16:06:02] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:06:02] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:06:02] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:06:02] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:06:02] ✅ 成功入队1个订单
[2025-08-21 16:06:02] ✅ 成功入队1个订单
[2025-08-21 16:06:02] 更新1个订单状态为'待更新'
[2025-08-21 16:06:02] 更新1个订单状态为'待更新'
[2025-08-21 16:06:02] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:06:02] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:06:02] 最终队列长度: 1
[2025-08-21 16:06:02] 最终队列长度: 1
[2025-08-21 16:06:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:06:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:09:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:09:01] ✅ Redis连接成功
[2025-08-21 16:09:01] ✅ Redis连接成功
[2025-08-21 16:09:01] 当前队列长度: 0
[2025-08-21 16:09:01] 当前队列长度: 0
[2025-08-21 16:09:01] 准备入队: 订单49 - 622429199710014822 - 上号中 - 
[2025-08-21 16:09:01] 准备入队: 订单49 - 622429199710014822 - 上号中 - 
[2025-08-21 16:09:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:09:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:09:01] 扫描到2个订单，其中{count(Array)}个需要入队
[2025-08-21 16:09:01] 扫描到2个订单，其中{count(Array)}个需要入队
[2025-08-21 16:09:01] ✅ 成功入队2个订单
[2025-08-21 16:09:01] ✅ 成功入队2个订单
[2025-08-21 16:09:01] 更新2个订单状态为'待更新'
[2025-08-21 16:09:01] 更新2个订单状态为'待更新'
[2025-08-21 16:09:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:09:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:09:01] 最终队列长度: 2
[2025-08-21 16:09:01] 最终队列长度: 2
[2025-08-21 16:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:09:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:12:01] ✅ Redis连接成功
[2025-08-21 16:12:01] ✅ Redis连接成功
[2025-08-21 16:12:01] 当前队列长度: 0
[2025-08-21 16:12:01] 当前队列长度: 0
[2025-08-21 16:12:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:12:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:12:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:12:01] 扫描到1个订单，其中{count(Array)}个需要入队
[2025-08-21 16:12:01] ✅ 成功入队1个订单
[2025-08-21 16:12:01] ✅ 成功入队1个订单
[2025-08-21 16:12:01] 更新1个订单状态为'待更新'
[2025-08-21 16:12:01] 更新1个订单状态为'待更新'
[2025-08-21 16:12:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:12:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:12:01] 最终队列长度: 1
[2025-08-21 16:12:01] 最终队列长度: 1
[2025-08-21 16:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:15:01] ✅ Redis连接成功
[2025-08-21 16:15:01] ✅ Redis连接成功
[2025-08-21 16:15:01] 当前队列长度: 0
[2025-08-21 16:15:01] 当前队列长度: 0
[2025-08-21 16:15:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:15:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:15:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:15:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:15:01] 扫描到2个订单，其中{count(Array)}个需要入队
[2025-08-21 16:15:01] 扫描到2个订单，其中{count(Array)}个需要入队
[2025-08-21 16:15:01] ✅ 成功入队2个订单
[2025-08-21 16:15:01] ✅ 成功入队2个订单
[2025-08-21 16:15:01] 更新2个订单状态为'待更新'
[2025-08-21 16:15:01] 更新2个订单状态为'待更新'
[2025-08-21 16:15:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:15:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:15:01] 最终队列长度: 2
[2025-08-21 16:15:01] 最终队列长度: 2
[2025-08-21 16:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:15:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:21:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:21:01] ✅ Redis连接成功
[2025-08-21 16:21:01] ✅ Redis连接成功
[2025-08-21 16:21:01] 当前队列长度: 0
[2025-08-21 16:21:01] 当前队列长度: 0
[2025-08-21 16:21:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:21:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:21:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:21:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:21:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:21:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:21:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:21:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:21:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:21:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:21:01] ✅ 成功入队4个订单
[2025-08-21 16:21:01] ✅ 成功入队4个订单
[2025-08-21 16:21:01] 更新4个订单状态为'待更新'
[2025-08-21 16:21:01] 更新4个订单状态为'待更新'
[2025-08-21 16:21:02] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:21:02] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:21:02] 最终队列长度: 4
[2025-08-21 16:21:02] 最终队列长度: 4
[2025-08-21 16:21:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:21:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:24:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:24:01] ✅ Redis连接成功
[2025-08-21 16:24:01] ✅ Redis连接成功
[2025-08-21 16:24:01] 当前队列长度: 0
[2025-08-21 16:24:01] 当前队列长度: 0
[2025-08-21 16:24:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:24:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:24:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:24:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:24:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:24:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:24:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:24:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:24:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:24:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:24:01] ✅ 成功入队4个订单
[2025-08-21 16:24:01] ✅ 成功入队4个订单
[2025-08-21 16:24:01] 更新4个订单状态为'待更新'
[2025-08-21 16:24:01] 更新4个订单状态为'待更新'
[2025-08-21 16:24:02] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:24:02] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:24:02] 最终队列长度: 4
[2025-08-21 16:24:02] 最终队列长度: 4
[2025-08-21 16:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:24:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:27:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:27:01] ✅ Redis连接成功
[2025-08-21 16:27:01] ✅ Redis连接成功
[2025-08-21 16:27:01] 当前队列长度: 0
[2025-08-21 16:27:01] 当前队列长度: 0
[2025-08-21 16:27:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:27:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:27:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:27:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:27:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:27:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:27:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:27:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:27:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:27:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:27:01] ✅ 成功入队4个订单
[2025-08-21 16:27:01] ✅ 成功入队4个订单
[2025-08-21 16:27:01] 更新4个订单状态为'待更新'
[2025-08-21 16:27:01] 更新4个订单状态为'待更新'
[2025-08-21 16:27:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:27:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:27:01] 最终队列长度: 4
[2025-08-21 16:27:01] 最终队列长度: 4
[2025-08-21 16:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:27:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:30:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:30:01] ✅ Redis连接成功
[2025-08-21 16:30:01] ✅ Redis连接成功
[2025-08-21 16:30:01] 当前队列长度: 0
[2025-08-21 16:30:01] 当前队列长度: 0
[2025-08-21 16:30:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:30:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:30:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:30:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:30:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:30:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:30:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:30:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:30:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:30:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:30:01] ✅ 成功入队4个订单
[2025-08-21 16:30:01] ✅ 成功入队4个订单
[2025-08-21 16:30:01] 更新4个订单状态为'待更新'
[2025-08-21 16:30:01] 更新4个订单状态为'待更新'
[2025-08-21 16:30:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:30:01] 守护进程状态: 1/{count(Array)} 个进程在运行
[2025-08-21 16:30:01] 最终队列长度: 4
[2025-08-21 16:30:01] 最终队列长度: 4
[2025-08-21 16:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:30:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:33:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:33:01] ✅ Redis连接成功
[2025-08-21 16:33:01] ✅ Redis连接成功
[2025-08-21 16:33:01] 当前队列长度: 0
[2025-08-21 16:33:01] 当前队列长度: 0
[2025-08-21 16:33:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:33:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:33:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:33:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:33:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:33:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:33:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:33:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:33:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:33:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:33:01] ✅ 成功入队4个订单
[2025-08-21 16:33:01] ✅ 成功入队4个订单
[2025-08-21 16:33:01] 更新4个订单状态为'待更新'
[2025-08-21 16:33:01] 更新4个订单状态为'待更新'
[2025-08-21 16:33:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:33:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:33:02] 最终队列长度: 4
[2025-08-21 16:33:02] 最终队列长度: 4
[2025-08-21 16:33:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:33:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:36:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:36:01] ✅ Redis连接成功
[2025-08-21 16:36:01] ✅ Redis连接成功
[2025-08-21 16:36:01] 当前队列长度: 0
[2025-08-21 16:36:01] 当前队列长度: 0
[2025-08-21 16:36:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:36:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:36:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:36:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:36:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:36:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:36:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:36:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:36:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:36:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:36:01] ✅ 成功入队4个订单
[2025-08-21 16:36:01] ✅ 成功入队4个订单
[2025-08-21 16:36:01] 更新4个订单状态为'待更新'
[2025-08-21 16:36:01] 更新4个订单状态为'待更新'
[2025-08-21 16:36:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:36:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:36:01] 最终队列长度: 4
[2025-08-21 16:36:01] 最终队列长度: 4
[2025-08-21 16:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:36:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:39:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:39:01] ✅ Redis连接成功
[2025-08-21 16:39:01] ✅ Redis连接成功
[2025-08-21 16:39:01] 当前队列长度: 0
[2025-08-21 16:39:01] 当前队列长度: 0
[2025-08-21 16:39:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 24.08%
[2025-08-21 16:39:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 24.08%
[2025-08-21 16:39:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:39:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:39:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:39:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:39:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:39:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:39:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:39:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:39:01] ✅ 成功入队4个订单
[2025-08-21 16:39:01] ✅ 成功入队4个订单
[2025-08-21 16:39:01] 更新4个订单状态为'待更新'
[2025-08-21 16:39:01] 更新4个订单状态为'待更新'
[2025-08-21 16:39:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:39:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:39:01] 最终队列长度: 4
[2025-08-21 16:39:01] 最终队列长度: 4
[2025-08-21 16:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:39:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:42:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:42:01] ✅ Redis连接成功
[2025-08-21 16:42:01] ✅ Redis连接成功
[2025-08-21 16:42:01] 当前队列长度: 0
[2025-08-21 16:42:01] 当前队列长度: 0
[2025-08-21 16:42:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:42:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:42:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:42:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:42:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:42:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:42:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:42:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:42:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:42:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:42:01] ✅ 成功入队4个订单
[2025-08-21 16:42:01] ✅ 成功入队4个订单
[2025-08-21 16:42:01] 更新4个订单状态为'待更新'
[2025-08-21 16:42:01] 更新4个订单状态为'待更新'
[2025-08-21 16:42:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:42:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:42:02] 最终队列长度: 4
[2025-08-21 16:42:02] 最终队列长度: 4
[2025-08-21 16:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:42:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:45:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:45:01] ✅ Redis连接成功
[2025-08-21 16:45:01] ✅ Redis连接成功
[2025-08-21 16:45:01] 当前队列长度: 0
[2025-08-21 16:45:01] 当前队列长度: 0
[2025-08-21 16:45:01] 准备入队: 订单54 - 13321327069 - 进行中 - 50%
[2025-08-21 16:45:01] 准备入队: 订单54 - 13321327069 - 进行中 - 50%
[2025-08-21 16:45:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:45:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:45:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:45:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:45:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:45:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:45:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:45:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:45:01] 扫描到5个订单，其中{count(Array)}个需要入队
[2025-08-21 16:45:01] 扫描到5个订单，其中{count(Array)}个需要入队
[2025-08-21 16:45:01] ✅ 成功入队5个订单
[2025-08-21 16:45:01] ✅ 成功入队5个订单
[2025-08-21 16:45:01] 更新5个订单状态为'待更新'
[2025-08-21 16:45:01] 更新5个订单状态为'待更新'
[2025-08-21 16:45:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:45:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:45:01] 最终队列长度: 5
[2025-08-21 16:45:01] 最终队列长度: 5
[2025-08-21 16:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:45:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:48:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:48:01] ✅ Redis连接成功
[2025-08-21 16:48:01] ✅ Redis连接成功
[2025-08-21 16:48:01] 当前队列长度: 0
[2025-08-21 16:48:01] 当前队列长度: 0
[2025-08-21 16:48:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:48:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:48:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:48:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:48:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:48:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:48:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:48:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:48:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:48:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:48:01] ✅ 成功入队4个订单
[2025-08-21 16:48:01] ✅ 成功入队4个订单
[2025-08-21 16:48:02] 更新4个订单状态为'待更新'
[2025-08-21 16:48:02] 更新4个订单状态为'待更新'
[2025-08-21 16:48:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:48:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:48:02] 最终队列长度: 4
[2025-08-21 16:48:02] 最终队列长度: 4
[2025-08-21 16:48:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:48:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:51:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:51:01] ✅ Redis连接成功
[2025-08-21 16:51:01] ✅ Redis连接成功
[2025-08-21 16:51:01] 当前队列长度: 0
[2025-08-21 16:51:01] 当前队列长度: 0
[2025-08-21 16:51:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:51:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:51:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:51:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:51:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:51:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:51:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:51:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:51:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:51:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:51:01] ✅ 成功入队4个订单
[2025-08-21 16:51:01] ✅ 成功入队4个订单
[2025-08-21 16:51:01] 更新4个订单状态为'待更新'
[2025-08-21 16:51:01] 更新4个订单状态为'待更新'
[2025-08-21 16:51:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:51:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:51:01] 最终队列长度: 4
[2025-08-21 16:51:01] 最终队列长度: 4
[2025-08-21 16:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:51:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:54:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:54:01] ✅ Redis连接成功
[2025-08-21 16:54:01] ✅ Redis连接成功
[2025-08-21 16:54:01] 当前队列长度: 0
[2025-08-21 16:54:01] 当前队列长度: 0
[2025-08-21 16:54:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:54:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:54:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:54:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:54:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:54:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:54:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:54:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:54:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:54:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:54:01] ✅ 成功入队4个订单
[2025-08-21 16:54:01] ✅ 成功入队4个订单
[2025-08-21 16:54:01] 更新4个订单状态为'待更新'
[2025-08-21 16:54:01] 更新4个订单状态为'待更新'
[2025-08-21 16:54:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:54:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:54:01] 最终队列长度: 4
[2025-08-21 16:54:01] 最终队列长度: 4
[2025-08-21 16:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:54:01] === 自动入队脚本执行完成 ===
[2025-08-21 16:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:57:01] === 自动入队脚本开始执行 ===
[2025-08-21 16:57:01] ✅ Redis连接成功
[2025-08-21 16:57:01] ✅ Redis连接成功
[2025-08-21 16:57:01] 当前队列长度: 1
[2025-08-21 16:57:01] 当前队列长度: 1
[2025-08-21 16:57:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:57:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 16:57:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:57:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 16:57:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:57:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 16:57:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:57:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 16:57:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:57:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 16:57:01] ✅ 成功入队4个订单
[2025-08-21 16:57:01] ✅ 成功入队4个订单
[2025-08-21 16:57:01] 更新4个订单状态为'待更新'
[2025-08-21 16:57:01] 更新4个订单状态为'待更新'
[2025-08-21 16:57:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:57:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 16:57:02] 最终队列长度: 5
[2025-08-21 16:57:02] 最终队列长度: 5
[2025-08-21 16:57:02] === 自动入队脚本执行完成 ===
[2025-08-21 16:57:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:00:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:00:01] ✅ Redis连接成功
[2025-08-21 17:00:01] ✅ Redis连接成功
[2025-08-21 17:00:01] 当前队列长度: 0
[2025-08-21 17:00:01] 当前队列长度: 0
[2025-08-21 17:00:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:00:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:00:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:00:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:00:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:00:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:00:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:00:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:00:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:00:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:00:01] ✅ 成功入队4个订单
[2025-08-21 17:00:01] ✅ 成功入队4个订单
[2025-08-21 17:00:01] 更新4个订单状态为'待更新'
[2025-08-21 17:00:01] 更新4个订单状态为'待更新'
[2025-08-21 17:00:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:00:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:00:01] 最终队列长度: 4
[2025-08-21 17:00:01] 最终队列长度: 4
[2025-08-21 17:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:00:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:03:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:03:01] ✅ Redis连接成功
[2025-08-21 17:03:01] ✅ Redis连接成功
[2025-08-21 17:03:01] 当前队列长度: 0
[2025-08-21 17:03:01] 当前队列长度: 0
[2025-08-21 17:03:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:03:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:03:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:03:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:03:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:03:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:03:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:03:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:03:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:03:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:03:01] ✅ 成功入队4个订单
[2025-08-21 17:03:01] ✅ 成功入队4个订单
[2025-08-21 17:03:01] 更新4个订单状态为'待更新'
[2025-08-21 17:03:01] 更新4个订单状态为'待更新'
[2025-08-21 17:03:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:03:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:03:01] 最终队列长度: 4
[2025-08-21 17:03:01] 最终队列长度: 4
[2025-08-21 17:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:03:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:06:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:06:01] ✅ Redis连接成功
[2025-08-21 17:06:01] ✅ Redis连接成功
[2025-08-21 17:06:01] 当前队列长度: 0
[2025-08-21 17:06:01] 当前队列长度: 0
[2025-08-21 17:06:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:06:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:06:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:06:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:06:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:06:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:06:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:06:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:06:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:06:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:06:01] ✅ 成功入队4个订单
[2025-08-21 17:06:01] ✅ 成功入队4个订单
[2025-08-21 17:06:01] 更新4个订单状态为'待更新'
[2025-08-21 17:06:01] 更新4个订单状态为'待更新'
[2025-08-21 17:06:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:06:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:06:02] 最终队列长度: 4
[2025-08-21 17:06:02] 最终队列长度: 4
[2025-08-21 17:06:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:06:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:09:02] === 自动入队脚本开始执行 ===
[2025-08-21 17:09:02] === 自动入队脚本开始执行 ===
[2025-08-21 17:09:02] ✅ Redis连接成功
[2025-08-21 17:09:02] ✅ Redis连接成功
[2025-08-21 17:09:02] 当前队列长度: 0
[2025-08-21 17:09:02] 当前队列长度: 0
[2025-08-21 17:09:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 53.44%
[2025-08-21 17:09:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 53.44%
[2025-08-21 17:09:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:09:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:09:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:09:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:09:02] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:09:02] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:09:02] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:09:02] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:09:02] ✅ 成功入队4个订单
[2025-08-21 17:09:02] ✅ 成功入队4个订单
[2025-08-21 17:09:02] 更新4个订单状态为'待更新'
[2025-08-21 17:09:02] 更新4个订单状态为'待更新'
[2025-08-21 17:09:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:09:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:09:02] 最终队列长度: 4
[2025-08-21 17:09:02] 最终队列长度: 4
[2025-08-21 17:09:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:09:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:11:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:11:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:11:01] ✅ Redis连接成功
[2025-08-21 17:11:01] ✅ Redis连接成功
[2025-08-21 17:11:01] 当前队列长度: 0
[2025-08-21 17:11:01] 当前队列长度: 0
[2025-08-21 17:11:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:11:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:11:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:11:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:11:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:11:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:11:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:11:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:11:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:11:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:11:01] ✅ 成功入队4个订单
[2025-08-21 17:11:01] ✅ 成功入队4个订单
[2025-08-21 17:11:01] 更新4个订单状态为'待更新'
[2025-08-21 17:11:01] 更新4个订单状态为'待更新'
[2025-08-21 17:11:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:11:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:11:01] 最终队列长度: 4
[2025-08-21 17:11:01] 最终队列长度: 4
[2025-08-21 17:11:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:11:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:12:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:12:01] ✅ Redis连接成功
[2025-08-21 17:12:01] ✅ Redis连接成功
[2025-08-21 17:12:01] 当前队列长度: 0
[2025-08-21 17:12:01] 当前队列长度: 0
[2025-08-21 17:12:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:12:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:12:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:12:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:12:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:12:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:12:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:12:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:12:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:12:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:12:01] ✅ 成功入队4个订单
[2025-08-21 17:12:01] ✅ 成功入队4个订单
[2025-08-21 17:12:01] 更新4个订单状态为'待更新'
[2025-08-21 17:12:01] 更新4个订单状态为'待更新'
[2025-08-21 17:12:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:12:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:12:01] 最终队列长度: 4
[2025-08-21 17:12:01] 最终队列长度: 4
[2025-08-21 17:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:12:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:13:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:13:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:13:01] ✅ Redis连接成功
[2025-08-21 17:13:01] ✅ Redis连接成功
[2025-08-21 17:13:01] 当前队列长度: 0
[2025-08-21 17:13:01] 当前队列长度: 0
[2025-08-21 17:13:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:13:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:13:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:13:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:13:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:13:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:13:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:13:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:13:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:13:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:13:01] ✅ 成功入队4个订单
[2025-08-21 17:13:01] ✅ 成功入队4个订单
[2025-08-21 17:13:01] 更新4个订单状态为'待更新'
[2025-08-21 17:13:01] 更新4个订单状态为'待更新'
[2025-08-21 17:13:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:13:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:13:01] 最终队列长度: 4
[2025-08-21 17:13:01] 最终队列长度: 4
[2025-08-21 17:13:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:13:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:14:02] === 自动入队脚本开始执行 ===
[2025-08-21 17:14:02] === 自动入队脚本开始执行 ===
[2025-08-21 17:14:02] ✅ Redis连接成功
[2025-08-21 17:14:02] ✅ Redis连接成功
[2025-08-21 17:14:02] 当前队列长度: 0
[2025-08-21 17:14:02] 当前队列长度: 0
[2025-08-21 17:14:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:14:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:14:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:14:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:14:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:14:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:14:02] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:14:02] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:14:02] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:14:02] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:14:02] ✅ 成功入队4个订单
[2025-08-21 17:14:02] ✅ 成功入队4个订单
[2025-08-21 17:14:02] 更新4个订单状态为'待更新'
[2025-08-21 17:14:02] 更新4个订单状态为'待更新'
[2025-08-21 17:14:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:14:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:14:02] 最终队列长度: 4
[2025-08-21 17:14:02] 最终队列长度: 4
[2025-08-21 17:14:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:14:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:15:01] === 自动入队脚本开始执行 ===
[2025-08-21 17:15:01] ✅ Redis连接成功
[2025-08-21 17:15:01] ✅ Redis连接成功
[2025-08-21 17:15:01] 当前队列长度: 0
[2025-08-21 17:15:01] 当前队列长度: 0
[2025-08-21 17:15:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:15:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:15:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:15:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:15:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:15:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:15:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:15:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:15:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:15:01] 扫描到4个订单，其中{count(Array)}个需要入队
[2025-08-21 17:15:01] ✅ 成功入队4个订单
[2025-08-21 17:15:01] ✅ 成功入队4个订单
[2025-08-21 17:15:01] 更新4个订单状态为'待更新'
[2025-08-21 17:15:01] 更新4个订单状态为'待更新'
[2025-08-21 17:15:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:15:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:15:02] 最终队列长度: 4
[2025-08-21 17:15:02] 最终队列长度: 4
[2025-08-21 17:15:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:15:02] === 自动入队脚本执行完成 ===
[2025-08-21 17:16:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:16:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:16:01] ✅ Redis连接成功
[2025-08-21 17:16:01] ✅ Redis连接成功
[2025-08-21 17:16:01] 当前队列长度: 0
[2025-08-21 17:16:01] 当前队列长度: 0
[2025-08-21 17:16:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:16:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:16:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:16:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:16:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:16:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:16:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:16:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:16:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:16:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:16:01] ✅ 成功入队4个订单
[2025-08-21 17:16:01] ✅ 成功入队4个订单
[2025-08-21 17:16:01] 更新4个订单状态为'待更新'
[2025-08-21 17:16:01] 更新4个订单状态为'待更新'
[2025-08-21 17:16:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:16:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:16:01] 最终队列长度: 0
[2025-08-21 17:16:01] 最终队列长度: 0
[2025-08-21 17:16:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:16:01] === 自动入队脚本执行完成 ===
[2025-08-21 17:17:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:17:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:17:01] ✅ Redis连接成功
[2025-08-21 17:17:01] ✅ Redis连接成功
[2025-08-21 17:17:01] 当前队列长度: 0
[2025-08-21 17:17:01] 当前队列长度: 0
[2025-08-21 17:17:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:17:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:17:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:17:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:17:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:17:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:17:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:17:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:17:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:17:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:17:01] ✅ 成功快速入队4个新订单
[2025-08-21 17:17:01] ✅ 成功快速入队4个新订单
[2025-08-21 17:17:01] 更新4个订单状态为'待更新'
[2025-08-21 17:17:01] 更新4个订单状态为'待更新'
[2025-08-21 17:17:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:17:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:17:01] 最终队列长度: 4
[2025-08-21 17:17:01] 最终队列长度: 4
[2025-08-21 17:17:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:17:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:18:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:18:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:18:01] ✅ Redis连接成功
[2025-08-21 17:18:01] ✅ Redis连接成功
[2025-08-21 17:18:01] 当前队列长度: 9
[2025-08-21 17:18:01] 当前队列长度: 9
[2025-08-21 17:18:01] 跳过: 订单51已在队列中
[2025-08-21 17:18:01] 跳过: 订单51已在队列中
[2025-08-21 17:18:01] 跳过: 订单52已在队列中
[2025-08-21 17:18:01] 跳过: 订单52已在队列中
[2025-08-21 17:18:01] 跳过: 订单50已在队列中
[2025-08-21 17:18:01] 跳过: 订单50已在队列中
[2025-08-21 17:18:01] 跳过: 订单48已在队列中
[2025-08-21 17:18:01] 跳过: 订单48已在队列中
[2025-08-21 17:18:01] 扫描到4个订单，其中0个需要快速入队
[2025-08-21 17:18:01] 扫描到4个订单，其中0个需要快速入队
[2025-08-21 17:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:18:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:18:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:18:01] 最终队列长度: 9
[2025-08-21 17:18:01] 最终队列长度: 9
[2025-08-21 17:18:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:18:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:19:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:19:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:19:01] ✅ Redis连接成功
[2025-08-21 17:19:01] ✅ Redis连接成功
[2025-08-21 17:19:01] 当前队列长度: 0
[2025-08-21 17:19:01] 当前队列长度: 0
[2025-08-21 17:19:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:19:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:19:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:19:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:19:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:19:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:19:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:19:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:19:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:19:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:19:01] ✅ 成功快速入队4个新订单
[2025-08-21 17:19:01] ✅ 成功快速入队4个新订单
[2025-08-21 17:19:01] 更新4个订单状态为'待更新'
[2025-08-21 17:19:01] 更新4个订单状态为'待更新'
[2025-08-21 17:19:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:19:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:19:01] 最终队列长度: 4
[2025-08-21 17:19:01] 最终队列长度: 4
[2025-08-21 17:19:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:19:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:20:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:20:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:20:01] ✅ Redis连接成功
[2025-08-21 17:20:01] ✅ Redis连接成功
[2025-08-21 17:20:01] 当前队列长度: 9
[2025-08-21 17:20:01] 当前队列长度: 9
[2025-08-21 17:20:01] 跳过: 订单51已在队列中
[2025-08-21 17:20:01] 跳过: 订单51已在队列中
[2025-08-21 17:20:01] 跳过: 订单52已在队列中
[2025-08-21 17:20:01] 跳过: 订单52已在队列中
[2025-08-21 17:20:01] 跳过: 订单50已在队列中
[2025-08-21 17:20:01] 跳过: 订单50已在队列中
[2025-08-21 17:20:01] 跳过: 订单48已在队列中
[2025-08-21 17:20:01] 跳过: 订单48已在队列中
[2025-08-21 17:20:01] 扫描到4个订单，其中0个需要快速入队
[2025-08-21 17:20:01] 扫描到4个订单，其中0个需要快速入队
[2025-08-21 17:20:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:20:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:20:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:20:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:20:01] 最终队列长度: 9
[2025-08-21 17:20:01] 最终队列长度: 9
[2025-08-21 17:20:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:20:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:21:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:21:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:21:01] ✅ Redis连接成功
[2025-08-21 17:21:01] ✅ Redis连接成功
[2025-08-21 17:21:01] 当前队列长度: 0
[2025-08-21 17:21:01] 当前队列长度: 0
[2025-08-21 17:21:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:21:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:21:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:21:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:21:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:21:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:21:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:21:01] 准备入队: 订单48 - 13649324102 - 运行中 - 20%
[2025-08-21 17:21:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:21:01] 扫描到4个订单，其中4个需要快速入队
[2025-08-21 17:21:01] ✅ 成功快速入队4个新订单
[2025-08-21 17:21:01] ✅ 成功快速入队4个新订单
[2025-08-21 17:21:01] 更新4个订单状态为'待更新'
[2025-08-21 17:21:01] 更新4个订单状态为'待更新'
[2025-08-21 17:21:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:21:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:21:01] 最终队列长度: 4
[2025-08-21 17:21:01] 最终队列长度: 4
[2025-08-21 17:21:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:21:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:22:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:22:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:22:01] ✅ Redis连接成功
[2025-08-21 17:22:01] ✅ Redis连接成功
[2025-08-21 17:22:01] 当前队列长度: 9
[2025-08-21 17:22:01] 当前队列长度: 9
[2025-08-21 17:22:01] 准备入队: 订单57 - 15814527759 - 上号中 - 
[2025-08-21 17:22:01] 准备入队: 订单57 - 15814527759 - 上号中 - 
[2025-08-21 17:22:01] 准备入队: 订单56 - 15814527759 - 上号中 - 
[2025-08-21 17:22:01] 准备入队: 订单56 - 15814527759 - 上号中 - 
[2025-08-21 17:22:01] 跳过: 订单51已在队列中
[2025-08-21 17:22:01] 跳过: 订单51已在队列中
[2025-08-21 17:22:01] 跳过: 订单52已在队列中
[2025-08-21 17:22:01] 跳过: 订单52已在队列中
[2025-08-21 17:22:01] 跳过: 订单50已在队列中
[2025-08-21 17:22:01] 跳过: 订单50已在队列中
[2025-08-21 17:22:01] 跳过: 订单48已在队列中
[2025-08-21 17:22:01] 跳过: 订单48已在队列中
[2025-08-21 17:22:01] 扫描到6个订单，其中2个需要快速入队
[2025-08-21 17:22:01] 扫描到6个订单，其中2个需要快速入队
[2025-08-21 17:22:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:22:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:22:01] 更新2个订单状态为'待更新'
[2025-08-21 17:22:01] 更新2个订单状态为'待更新'
[2025-08-21 17:22:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:22:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:22:01] 最终队列长度: 11
[2025-08-21 17:22:01] 最终队列长度: 11
[2025-08-21 17:22:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:22:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:23:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:23:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:23:02] ✅ Redis连接成功
[2025-08-21 17:23:02] ✅ Redis连接成功
[2025-08-21 17:23:02] 当前队列长度: 0
[2025-08-21 17:23:02] 当前队列长度: 0
[2025-08-21 17:23:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:23:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:23:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:23:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:23:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:23:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:23:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:23:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:23:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:23:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:23:02] 更新3个订单状态为'待更新'
[2025-08-21 17:23:02] 更新3个订单状态为'待更新'
[2025-08-21 17:23:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:23:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:23:02] 最终队列长度: 3
[2025-08-21 17:23:02] 最终队列长度: 3
[2025-08-21 17:23:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:23:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:24:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:24:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:24:01] ✅ Redis连接成功
[2025-08-21 17:24:01] ✅ Redis连接成功
[2025-08-21 17:24:01] 当前队列长度: 9
[2025-08-21 17:24:01] 当前队列长度: 9
[2025-08-21 17:24:01] 跳过: 订单51已在队列中
[2025-08-21 17:24:01] 跳过: 订单51已在队列中
[2025-08-21 17:24:01] 跳过: 订单52已在队列中
[2025-08-21 17:24:01] 跳过: 订单52已在队列中
[2025-08-21 17:24:01] 跳过: 订单50已在队列中
[2025-08-21 17:24:01] 跳过: 订单50已在队列中
[2025-08-21 17:24:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:24:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:24:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:24:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:24:02] 最终队列长度: 9
[2025-08-21 17:24:02] 最终队列长度: 9
[2025-08-21 17:24:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:24:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:25:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:25:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:25:01] ✅ Redis连接成功
[2025-08-21 17:25:01] ✅ Redis连接成功
[2025-08-21 17:25:01] 当前队列长度: 0
[2025-08-21 17:25:01] 当前队列长度: 0
[2025-08-21 17:25:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:25:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:25:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:25:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:25:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:25:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:25:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:25:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:25:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:25:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:25:01] 更新3个订单状态为'待更新'
[2025-08-21 17:25:01] 更新3个订单状态为'待更新'
[2025-08-21 17:25:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:25:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:25:02] 最终队列长度: 3
[2025-08-21 17:25:02] 最终队列长度: 3
[2025-08-21 17:25:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:25:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:26:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:26:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:26:01] ✅ Redis连接成功
[2025-08-21 17:26:01] ✅ Redis连接成功
[2025-08-21 17:26:01] 当前队列长度: 0
[2025-08-21 17:26:01] 当前队列长度: 0
[2025-08-21 17:26:01] 跳过: 订单51已在队列中
[2025-08-21 17:26:01] 跳过: 订单51已在队列中
[2025-08-21 17:26:01] 跳过: 订单52已在队列中
[2025-08-21 17:26:01] 跳过: 订单52已在队列中
[2025-08-21 17:26:01] 跳过: 订单50已在队列中
[2025-08-21 17:26:01] 跳过: 订单50已在队列中
[2025-08-21 17:26:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:26:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:26:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:26:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:26:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:26:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:26:02] 最终队列长度: 9
[2025-08-21 17:26:02] 最终队列长度: 9
[2025-08-21 17:26:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:26:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:27:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:27:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:27:01] ✅ Redis连接成功
[2025-08-21 17:27:01] ✅ Redis连接成功
[2025-08-21 17:27:01] 当前队列长度: 0
[2025-08-21 17:27:01] 当前队列长度: 0
[2025-08-21 17:27:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:27:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:27:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:27:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:27:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:27:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:27:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:27:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:27:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:27:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:27:01] 更新3个订单状态为'待更新'
[2025-08-21 17:27:01] 更新3个订单状态为'待更新'
[2025-08-21 17:27:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:27:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:27:01] 最终队列长度: 3
[2025-08-21 17:27:01] 最终队列长度: 3
[2025-08-21 17:27:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:27:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:28:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:28:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:28:01] ✅ Redis连接成功
[2025-08-21 17:28:01] ✅ Redis连接成功
[2025-08-21 17:28:01] 当前队列长度: 0
[2025-08-21 17:28:01] 当前队列长度: 0
[2025-08-21 17:28:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:28:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:28:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:28:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:28:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:28:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:28:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:28:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:28:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:28:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:28:01] 更新3个订单状态为'待更新'
[2025-08-21 17:28:01] 更新3个订单状态为'待更新'
[2025-08-21 17:28:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:28:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:28:01] 最终队列长度: 9
[2025-08-21 17:28:01] 最终队列长度: 9
[2025-08-21 17:28:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:28:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:29:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:29:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:29:01] ✅ Redis连接成功
[2025-08-21 17:29:01] ✅ Redis连接成功
[2025-08-21 17:29:01] 当前队列长度: 0
[2025-08-21 17:29:01] 当前队列长度: 0
[2025-08-21 17:29:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:29:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:29:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:29:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:29:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:29:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:29:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:29:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:29:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:29:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:29:01] 更新3个订单状态为'待更新'
[2025-08-21 17:29:01] 更新3个订单状态为'待更新'
[2025-08-21 17:29:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:29:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:29:01] 最终队列长度: 3
[2025-08-21 17:29:01] 最终队列长度: 3
[2025-08-21 17:29:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:29:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:30:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:30:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:30:01] ✅ Redis连接成功
[2025-08-21 17:30:01] ✅ Redis连接成功
[2025-08-21 17:30:01] 当前队列长度: 9
[2025-08-21 17:30:01] 当前队列长度: 9
[2025-08-21 17:30:01] 跳过: 订单51已在队列中
[2025-08-21 17:30:01] 跳过: 订单51已在队列中
[2025-08-21 17:30:01] 跳过: 订单52已在队列中
[2025-08-21 17:30:01] 跳过: 订单52已在队列中
[2025-08-21 17:30:01] 跳过: 订单50已在队列中
[2025-08-21 17:30:01] 跳过: 订单50已在队列中
[2025-08-21 17:30:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:30:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:30:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:30:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:30:01] 最终队列长度: 9
[2025-08-21 17:30:01] 最终队列长度: 9
[2025-08-21 17:30:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:30:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:31:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:31:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:31:02] ✅ Redis连接成功
[2025-08-21 17:31:02] ✅ Redis连接成功
[2025-08-21 17:31:02] 当前队列长度: 0
[2025-08-21 17:31:02] 当前队列长度: 0
[2025-08-21 17:31:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:31:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:31:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:31:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:31:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:31:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:31:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:31:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:31:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:31:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:31:02] 更新3个订单状态为'待更新'
[2025-08-21 17:31:02] 更新3个订单状态为'待更新'
[2025-08-21 17:31:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:31:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:31:02] 最终队列长度: 3
[2025-08-21 17:31:02] 最终队列长度: 3
[2025-08-21 17:31:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:31:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:32:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:32:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:32:01] ✅ Redis连接成功
[2025-08-21 17:32:01] ✅ Redis连接成功
[2025-08-21 17:32:01] 当前队列长度: 9
[2025-08-21 17:32:01] 当前队列长度: 9
[2025-08-21 17:32:01] 跳过: 订单51已在队列中
[2025-08-21 17:32:01] 跳过: 订单51已在队列中
[2025-08-21 17:32:01] 跳过: 订单52已在队列中
[2025-08-21 17:32:01] 跳过: 订单52已在队列中
[2025-08-21 17:32:01] 跳过: 订单50已在队列中
[2025-08-21 17:32:01] 跳过: 订单50已在队列中
[2025-08-21 17:32:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:32:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:32:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:32:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:32:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:32:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:32:02] 最终队列长度: 9
[2025-08-21 17:32:02] 最终队列长度: 9
[2025-08-21 17:32:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:32:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:33:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:33:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:33:01] ✅ Redis连接成功
[2025-08-21 17:33:01] ✅ Redis连接成功
[2025-08-21 17:33:01] 当前队列长度: 0
[2025-08-21 17:33:01] 当前队列长度: 0
[2025-08-21 17:33:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:33:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:33:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:33:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:33:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:33:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:33:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:33:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:33:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:33:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:33:01] 更新3个订单状态为'待更新'
[2025-08-21 17:33:01] 更新3个订单状态为'待更新'
[2025-08-21 17:33:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:33:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:33:02] 最终队列长度: 3
[2025-08-21 17:33:02] 最终队列长度: 3
[2025-08-21 17:33:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:33:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:34:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:34:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:34:01] ✅ Redis连接成功
[2025-08-21 17:34:01] ✅ Redis连接成功
[2025-08-21 17:34:01] 当前队列长度: 9
[2025-08-21 17:34:01] 当前队列长度: 9
[2025-08-21 17:34:01] 跳过: 订单51已在队列中
[2025-08-21 17:34:01] 跳过: 订单51已在队列中
[2025-08-21 17:34:01] 跳过: 订单52已在队列中
[2025-08-21 17:34:01] 跳过: 订单52已在队列中
[2025-08-21 17:34:01] 跳过: 订单50已在队列中
[2025-08-21 17:34:01] 跳过: 订单50已在队列中
[2025-08-21 17:34:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:34:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:34:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:34:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:34:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:34:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:34:01] 最终队列长度: 9
[2025-08-21 17:34:01] 最终队列长度: 9
[2025-08-21 17:34:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:34:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:35:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:35:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:35:01] ✅ Redis连接成功
[2025-08-21 17:35:01] ✅ Redis连接成功
[2025-08-21 17:35:01] 当前队列长度: 0
[2025-08-21 17:35:01] 当前队列长度: 0
[2025-08-21 17:35:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:35:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:35:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:35:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:35:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:35:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:35:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:35:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:35:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:35:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:35:01] 更新3个订单状态为'待更新'
[2025-08-21 17:35:01] 更新3个订单状态为'待更新'
[2025-08-21 17:35:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:35:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:35:02] 最终队列长度: 3
[2025-08-21 17:35:02] 最终队列长度: 3
[2025-08-21 17:35:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:35:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:36:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:36:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:36:01] ✅ Redis连接成功
[2025-08-21 17:36:01] ✅ Redis连接成功
[2025-08-21 17:36:01] 当前队列长度: 9
[2025-08-21 17:36:01] 当前队列长度: 9
[2025-08-21 17:36:01] 跳过: 订单51已在队列中
[2025-08-21 17:36:01] 跳过: 订单51已在队列中
[2025-08-21 17:36:01] 跳过: 订单52已在队列中
[2025-08-21 17:36:01] 跳过: 订单52已在队列中
[2025-08-21 17:36:01] 跳过: 订单50已在队列中
[2025-08-21 17:36:01] 跳过: 订单50已在队列中
[2025-08-21 17:36:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:36:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:36:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:36:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:36:01] 最终队列长度: 9
[2025-08-21 17:36:01] 最终队列长度: 9
[2025-08-21 17:36:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:36:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:37:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:37:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:37:01] ✅ Redis连接成功
[2025-08-21 17:37:01] ✅ Redis连接成功
[2025-08-21 17:37:01] 当前队列长度: 0
[2025-08-21 17:37:01] 当前队列长度: 0
[2025-08-21 17:37:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:37:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:37:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:37:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:37:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:37:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:37:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:37:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:37:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:37:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:37:01] 更新3个订单状态为'待更新'
[2025-08-21 17:37:01] 更新3个订单状态为'待更新'
[2025-08-21 17:37:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:37:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:37:01] 最终队列长度: 3
[2025-08-21 17:37:01] 最终队列长度: 3
[2025-08-21 17:37:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:37:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:38:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:38:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:38:01] ✅ Redis连接成功
[2025-08-21 17:38:01] ✅ Redis连接成功
[2025-08-21 17:38:01] 当前队列长度: 0
[2025-08-21 17:38:01] 当前队列长度: 0
[2025-08-21 17:38:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:38:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:38:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:38:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:38:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:38:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:38:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:38:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:38:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:38:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:38:01] 更新3个订单状态为'待更新'
[2025-08-21 17:38:01] 更新3个订单状态为'待更新'
[2025-08-21 17:38:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:38:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:38:01] 最终队列长度: 9
[2025-08-21 17:38:01] 最终队列长度: 9
[2025-08-21 17:38:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:38:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:39:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:39:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:39:02] ✅ Redis连接成功
[2025-08-21 17:39:02] ✅ Redis连接成功
[2025-08-21 17:39:02] 当前队列长度: 0
[2025-08-21 17:39:02] 当前队列长度: 0
[2025-08-21 17:39:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:39:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:39:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:39:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:39:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:39:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:39:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:39:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:39:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:39:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:39:02] 更新3个订单状态为'待更新'
[2025-08-21 17:39:02] 更新3个订单状态为'待更新'
[2025-08-21 17:39:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:39:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:39:02] 最终队列长度: 3
[2025-08-21 17:39:02] 最终队列长度: 3
[2025-08-21 17:39:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:39:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:40:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:40:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:40:01] ✅ Redis连接成功
[2025-08-21 17:40:01] ✅ Redis连接成功
[2025-08-21 17:40:01] 当前队列长度: 0
[2025-08-21 17:40:01] 当前队列长度: 0
[2025-08-21 17:40:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:40:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:40:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:40:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:40:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:40:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:40:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:40:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:40:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:40:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:40:01] 更新3个订单状态为'待更新'
[2025-08-21 17:40:01] 更新3个订单状态为'待更新'
[2025-08-21 17:40:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:40:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:40:02] 最终队列长度: 9
[2025-08-21 17:40:02] 最终队列长度: 9
[2025-08-21 17:40:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:40:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:41:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:41:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:41:01] ✅ Redis连接成功
[2025-08-21 17:41:01] ✅ Redis连接成功
[2025-08-21 17:41:01] 当前队列长度: 0
[2025-08-21 17:41:01] 当前队列长度: 0
[2025-08-21 17:41:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:41:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 17:41:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:41:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:41:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:41:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:41:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:41:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:41:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:41:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:41:01] 更新3个订单状态为'待更新'
[2025-08-21 17:41:01] 更新3个订单状态为'待更新'
[2025-08-21 17:41:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:41:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:41:02] 最终队列长度: 3
[2025-08-21 17:41:02] 最终队列长度: 3
[2025-08-21 17:41:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:41:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:42:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:42:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:42:02] ✅ Redis连接成功
[2025-08-21 17:42:02] ✅ Redis连接成功
[2025-08-21 17:42:02] 当前队列长度: 9
[2025-08-21 17:42:02] 当前队列长度: 9
[2025-08-21 17:42:02] 跳过: 订单51已在队列中
[2025-08-21 17:42:02] 跳过: 订单51已在队列中
[2025-08-21 17:42:02] 跳过: 订单52已在队列中
[2025-08-21 17:42:02] 跳过: 订单52已在队列中
[2025-08-21 17:42:02] 跳过: 订单50已在队列中
[2025-08-21 17:42:02] 跳过: 订单50已在队列中
[2025-08-21 17:42:02] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:42:02] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:42:02] ℹ️ 没有新订单需要入队
[2025-08-21 17:42:02] ℹ️ 没有新订单需要入队
[2025-08-21 17:42:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:42:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:42:02] 最终队列长度: 9
[2025-08-21 17:42:02] 最终队列长度: 9
[2025-08-21 17:42:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:42:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:43:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:43:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:43:02] ✅ Redis连接成功
[2025-08-21 17:43:02] ✅ Redis连接成功
[2025-08-21 17:43:02] 当前队列长度: 0
[2025-08-21 17:43:02] 当前队列长度: 0
[2025-08-21 17:43:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 91.22%
[2025-08-21 17:43:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 91.22%
[2025-08-21 17:43:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:43:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:43:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:43:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:43:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:43:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:43:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:43:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:43:02] 更新3个订单状态为'待更新'
[2025-08-21 17:43:02] 更新3个订单状态为'待更新'
[2025-08-21 17:43:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:43:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:43:02] 最终队列长度: 3
[2025-08-21 17:43:02] 最终队列长度: 3
[2025-08-21 17:43:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:43:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:44:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:44:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:44:01] ✅ Redis连接成功
[2025-08-21 17:44:01] ✅ Redis连接成功
[2025-08-21 17:44:01] 当前队列长度: 9
[2025-08-21 17:44:01] 当前队列长度: 9
[2025-08-21 17:44:01] 跳过: 订单51已在队列中
[2025-08-21 17:44:01] 跳过: 订单51已在队列中
[2025-08-21 17:44:01] 跳过: 订单52已在队列中
[2025-08-21 17:44:01] 跳过: 订单52已在队列中
[2025-08-21 17:44:01] 跳过: 订单50已在队列中
[2025-08-21 17:44:01] 跳过: 订单50已在队列中
[2025-08-21 17:44:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:44:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:44:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:44:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:44:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:44:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:44:01] 最终队列长度: 9
[2025-08-21 17:44:01] 最终队列长度: 9
[2025-08-21 17:44:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:44:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:45:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:45:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:45:02] ✅ Redis连接成功
[2025-08-21 17:45:02] ✅ Redis连接成功
[2025-08-21 17:45:02] 当前队列长度: 0
[2025-08-21 17:45:02] 当前队列长度: 0
[2025-08-21 17:45:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 96.28%
[2025-08-21 17:45:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 96.28%
[2025-08-21 17:45:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:45:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:45:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:45:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:45:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:45:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:45:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:45:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:45:02] 更新3个订单状态为'待更新'
[2025-08-21 17:45:02] 更新3个订单状态为'待更新'
[2025-08-21 17:45:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:45:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:45:02] 最终队列长度: 3
[2025-08-21 17:45:02] 最终队列长度: 3
[2025-08-21 17:45:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:45:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:46:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:46:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:46:01] ✅ Redis连接成功
[2025-08-21 17:46:01] ✅ Redis连接成功
[2025-08-21 17:46:01] 当前队列长度: 0
[2025-08-21 17:46:01] 当前队列长度: 0
[2025-08-21 17:46:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 97.52%
[2025-08-21 17:46:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 97.52%
[2025-08-21 17:46:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:46:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:46:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:46:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:46:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:46:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:46:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:46:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:46:01] 更新3个订单状态为'待更新'
[2025-08-21 17:46:01] 更新3个订单状态为'待更新'
[2025-08-21 17:46:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:46:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:46:01] 最终队列长度: 9
[2025-08-21 17:46:01] 最终队列长度: 9
[2025-08-21 17:46:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:46:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:47:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:47:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:47:01] ✅ Redis连接成功
[2025-08-21 17:47:01] ✅ Redis连接成功
[2025-08-21 17:47:01] 当前队列长度: 0
[2025-08-21 17:47:01] 当前队列长度: 0
[2025-08-21 17:47:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 97.52%
[2025-08-21 17:47:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 97.52%
[2025-08-21 17:47:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:47:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:47:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:47:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:47:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:47:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:47:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:47:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:47:01] 更新3个订单状态为'待更新'
[2025-08-21 17:47:01] 更新3个订单状态为'待更新'
[2025-08-21 17:47:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:47:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:47:02] 最终队列长度: 3
[2025-08-21 17:47:02] 最终队列长度: 3
[2025-08-21 17:47:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:47:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:48:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:48:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:48:01] ✅ Redis连接成功
[2025-08-21 17:48:01] ✅ Redis连接成功
[2025-08-21 17:48:01] 当前队列长度: 9
[2025-08-21 17:48:01] 当前队列长度: 9
[2025-08-21 17:48:01] 跳过: 订单51已在队列中
[2025-08-21 17:48:01] 跳过: 订单51已在队列中
[2025-08-21 17:48:01] 跳过: 订单52已在队列中
[2025-08-21 17:48:01] 跳过: 订单52已在队列中
[2025-08-21 17:48:01] 跳过: 订单50已在队列中
[2025-08-21 17:48:01] 跳过: 订单50已在队列中
[2025-08-21 17:48:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:48:01] 扫描到3个订单，其中0个需要快速入队
[2025-08-21 17:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:48:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:48:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:48:01] 最终队列长度: 9
[2025-08-21 17:48:01] 最终队列长度: 9
[2025-08-21 17:48:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:48:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:49:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:49:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:49:01] ✅ Redis连接成功
[2025-08-21 17:49:01] ✅ Redis连接成功
[2025-08-21 17:49:01] 当前队列长度: 0
[2025-08-21 17:49:01] 当前队列长度: 0
[2025-08-21 17:49:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 1.62%
[2025-08-21 17:49:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 1.62%
[2025-08-21 17:49:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:49:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:49:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:49:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:49:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:49:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:49:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:49:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:49:01] 更新3个订单状态为'待更新'
[2025-08-21 17:49:01] 更新3个订单状态为'待更新'
[2025-08-21 17:49:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:49:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:49:01] 最终队列长度: 3
[2025-08-21 17:49:01] 最终队列长度: 3
[2025-08-21 17:49:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:49:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:50:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:50:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:50:01] ✅ Redis连接成功
[2025-08-21 17:50:01] ✅ Redis连接成功
[2025-08-21 17:50:01] 当前队列长度: 0
[2025-08-21 17:50:01] 当前队列长度: 0
[2025-08-21 17:50:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 5.95%
[2025-08-21 17:50:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 5.95%
[2025-08-21 17:50:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:50:01] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:50:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:50:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:50:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:50:01] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:50:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:50:01] ✅ 成功快速入队3个新订单
[2025-08-21 17:50:01] 更新3个订单状态为'待更新'
[2025-08-21 17:50:01] 更新3个订单状态为'待更新'
[2025-08-21 17:50:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:50:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:50:01] 最终队列长度: 9
[2025-08-21 17:50:01] 最终队列长度: 9
[2025-08-21 17:50:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:50:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:51:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:51:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:51:02] ✅ Redis连接成功
[2025-08-21 17:51:02] ✅ Redis连接成功
[2025-08-21 17:51:02] 当前队列长度: 0
[2025-08-21 17:51:02] 当前队列长度: 0
[2025-08-21 17:51:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 8.66%
[2025-08-21 17:51:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 8.66%
[2025-08-21 17:51:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:51:02] 准备入队: 订单52 - 622429198901150742 - 队列中 - 0%
[2025-08-21 17:51:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:51:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:51:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:51:02] 扫描到3个订单，其中3个需要快速入队
[2025-08-21 17:51:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:51:02] ✅ 成功快速入队3个新订单
[2025-08-21 17:51:02] 更新3个订单状态为'待更新'
[2025-08-21 17:51:02] 更新3个订单状态为'待更新'
[2025-08-21 17:51:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:51:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:51:02] 最终队列长度: 3
[2025-08-21 17:51:02] 最终队列长度: 3
[2025-08-21 17:51:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:51:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:52:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:52:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:52:01] ✅ Redis连接成功
[2025-08-21 17:52:01] ✅ Redis连接成功
[2025-08-21 17:52:01] 当前队列长度: 0
[2025-08-21 17:52:01] 当前队列长度: 0
[2025-08-21 17:52:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 10.02%
[2025-08-21 17:52:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 10.02%
[2025-08-21 17:52:01] 跳过: 订单52已在队列中
[2025-08-21 17:52:01] 跳过: 订单52已在队列中
[2025-08-21 17:52:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:52:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:52:01] 扫描到3个订单，其中2个需要快速入队
[2025-08-21 17:52:01] 扫描到3个订单，其中2个需要快速入队
[2025-08-21 17:52:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:52:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:52:01] 更新2个订单状态为'待更新'
[2025-08-21 17:52:01] 更新2个订单状态为'待更新'
[2025-08-21 17:52:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:52:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:52:02] 最终队列长度: 11
[2025-08-21 17:52:02] 最终队列长度: 11
[2025-08-21 17:52:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:52:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:53:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:53:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:53:01] ✅ Redis连接成功
[2025-08-21 17:53:01] ✅ Redis连接成功
[2025-08-21 17:53:01] 当前队列长度: 0
[2025-08-21 17:53:01] 当前队列长度: 0
[2025-08-21 17:53:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 12.96%
[2025-08-21 17:53:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 12.96%
[2025-08-21 17:53:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:53:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:53:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:53:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:53:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:53:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:53:01] 更新2个订单状态为'待更新'
[2025-08-21 17:53:01] 更新2个订单状态为'待更新'
[2025-08-21 17:53:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:53:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:53:01] 最终队列长度: 2
[2025-08-21 17:53:01] 最终队列长度: 2
[2025-08-21 17:53:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:53:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:54:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:54:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:54:01] ✅ Redis连接成功
[2025-08-21 17:54:01] ✅ Redis连接成功
[2025-08-21 17:54:01] 当前队列长度: 0
[2025-08-21 17:54:01] 当前队列长度: 0
[2025-08-21 17:54:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 15.58%
[2025-08-21 17:54:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 15.58%
[2025-08-21 17:54:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:54:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:54:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:54:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:54:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:54:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:54:01] 更新2个订单状态为'待更新'
[2025-08-21 17:54:01] 更新2个订单状态为'待更新'
[2025-08-21 17:54:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:54:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:54:01] 最终队列长度: 8
[2025-08-21 17:54:01] 最终队列长度: 8
[2025-08-21 17:54:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:54:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:55:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:55:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:55:01] ✅ Redis连接成功
[2025-08-21 17:55:01] ✅ Redis连接成功
[2025-08-21 17:55:01] 当前队列长度: 0
[2025-08-21 17:55:01] 当前队列长度: 0
[2025-08-21 17:55:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 18.29%
[2025-08-21 17:55:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 18.29%
[2025-08-21 17:55:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:55:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:55:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:55:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:55:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:55:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:55:01] 更新2个订单状态为'待更新'
[2025-08-21 17:55:01] 更新2个订单状态为'待更新'
[2025-08-21 17:55:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:55:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:55:01] 最终队列长度: 2
[2025-08-21 17:55:01] 最终队列长度: 2
[2025-08-21 17:55:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:55:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:56:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:56:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:56:01] ✅ Redis连接成功
[2025-08-21 17:56:01] ✅ Redis连接成功
[2025-08-21 17:56:01] 当前队列长度: 0
[2025-08-21 17:56:01] 当前队列长度: 0
[2025-08-21 17:56:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 19.6%
[2025-08-21 17:56:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 19.6%
[2025-08-21 17:56:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:56:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:56:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:56:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:56:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:56:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:56:01] 更新2个订单状态为'待更新'
[2025-08-21 17:56:01] 更新2个订单状态为'待更新'
[2025-08-21 17:56:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:56:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:56:01] 最终队列长度: 0
[2025-08-21 17:56:01] 最终队列长度: 0
[2025-08-21 17:56:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:56:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:57:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:57:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:57:01] ✅ Redis连接成功
[2025-08-21 17:57:01] ✅ Redis连接成功
[2025-08-21 17:57:01] 当前队列长度: 0
[2025-08-21 17:57:01] 当前队列长度: 0
[2025-08-21 17:57:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 23.23%
[2025-08-21 17:57:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 23.23%
[2025-08-21 17:57:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:57:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:57:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:57:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:57:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:57:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:57:01] 更新2个订单状态为'待更新'
[2025-08-21 17:57:01] 更新2个订单状态为'待更新'
[2025-08-21 17:57:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:57:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:57:01] 最终队列长度: 2
[2025-08-21 17:57:01] 最终队列长度: 2
[2025-08-21 17:57:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:57:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:58:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:58:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:58:01] ✅ Redis连接成功
[2025-08-21 17:58:01] ✅ Redis连接成功
[2025-08-21 17:58:01] 当前队列长度: 8
[2025-08-21 17:58:01] 当前队列长度: 8
[2025-08-21 17:58:01] 跳过: 订单51已在队列中
[2025-08-21 17:58:01] 跳过: 订单51已在队列中
[2025-08-21 17:58:01] 跳过: 订单50已在队列中
[2025-08-21 17:58:01] 跳过: 订单50已在队列中
[2025-08-21 17:58:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 17:58:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 17:58:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:58:01] ℹ️ 没有新订单需要入队
[2025-08-21 17:58:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:58:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:58:02] 最终队列长度: 8
[2025-08-21 17:58:02] 最终队列长度: 8
[2025-08-21 17:58:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:58:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:59:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:59:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 17:59:01] ✅ Redis连接成功
[2025-08-21 17:59:01] ✅ Redis连接成功
[2025-08-21 17:59:01] 当前队列长度: 0
[2025-08-21 17:59:01] 当前队列长度: 0
[2025-08-21 17:59:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 28.6%
[2025-08-21 17:59:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 28.6%
[2025-08-21 17:59:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:59:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 17:59:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:59:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 17:59:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:59:01] ✅ 成功快速入队2个新订单
[2025-08-21 17:59:01] 更新2个订单状态为'待更新'
[2025-08-21 17:59:01] 更新2个订单状态为'待更新'
[2025-08-21 17:59:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:59:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 17:59:01] 最终队列长度: 2
[2025-08-21 17:59:01] 最终队列长度: 2
[2025-08-21 17:59:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 17:59:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:00:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:00:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:00:01] ✅ Redis连接成功
[2025-08-21 18:00:01] ✅ Redis连接成功
[2025-08-21 18:00:01] 当前队列长度: 8
[2025-08-21 18:00:01] 当前队列长度: 8
[2025-08-21 18:00:01] 跳过: 订单51已在队列中
[2025-08-21 18:00:01] 跳过: 订单51已在队列中
[2025-08-21 18:00:01] 跳过: 订单50已在队列中
[2025-08-21 18:00:01] 跳过: 订单50已在队列中
[2025-08-21 18:00:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:00:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:00:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:00:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:00:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:00:01] 最终队列长度: 8
[2025-08-21 18:00:01] 最终队列长度: 8
[2025-08-21 18:00:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:00:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:01:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:01:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:01:01] ✅ Redis连接成功
[2025-08-21 18:01:01] ✅ Redis连接成功
[2025-08-21 18:01:01] 当前队列长度: 0
[2025-08-21 18:01:01] 当前队列长度: 0
[2025-08-21 18:01:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 33.41%
[2025-08-21 18:01:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 33.41%
[2025-08-21 18:01:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:01:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:01:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:01:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:01:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:01:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:01:01] 更新2个订单状态为'待更新'
[2025-08-21 18:01:01] 更新2个订单状态为'待更新'
[2025-08-21 18:01:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:01:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:01:01] 最终队列长度: 2
[2025-08-21 18:01:01] 最终队列长度: 2
[2025-08-21 18:01:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:01:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:02:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:02:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:02:01] ✅ Redis连接成功
[2025-08-21 18:02:01] ✅ Redis连接成功
[2025-08-21 18:02:01] 当前队列长度: 8
[2025-08-21 18:02:01] 当前队列长度: 8
[2025-08-21 18:02:01] 跳过: 订单51已在队列中
[2025-08-21 18:02:01] 跳过: 订单51已在队列中
[2025-08-21 18:02:01] 跳过: 订单50已在队列中
[2025-08-21 18:02:01] 跳过: 订单50已在队列中
[2025-08-21 18:02:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:02:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:02:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:02:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:02:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:02:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:02:01] 最终队列长度: 8
[2025-08-21 18:02:01] 最终队列长度: 8
[2025-08-21 18:02:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:02:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:03:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:03:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:03:01] ✅ Redis连接成功
[2025-08-21 18:03:01] ✅ Redis连接成功
[2025-08-21 18:03:01] 当前队列长度: 0
[2025-08-21 18:03:01] 当前队列长度: 0
[2025-08-21 18:03:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 39.05%
[2025-08-21 18:03:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 39.05%
[2025-08-21 18:03:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:03:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:03:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:03:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:03:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:03:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:03:01] 更新2个订单状态为'待更新'
[2025-08-21 18:03:01] 更新2个订单状态为'待更新'
[2025-08-21 18:03:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:03:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:03:01] 最终队列长度: 2
[2025-08-21 18:03:01] 最终队列长度: 2
[2025-08-21 18:03:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:03:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:04:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:04:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:04:02] ✅ Redis连接成功
[2025-08-21 18:04:02] ✅ Redis连接成功
[2025-08-21 18:04:02] 当前队列长度: 0
[2025-08-21 18:04:02] 当前队列长度: 0
[2025-08-21 18:04:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 40.36%
[2025-08-21 18:04:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 40.36%
[2025-08-21 18:04:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:04:02] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:04:02] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:04:02] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:04:02] ✅ 成功快速入队2个新订单
[2025-08-21 18:04:02] ✅ 成功快速入队2个新订单
[2025-08-21 18:04:02] 更新2个订单状态为'待更新'
[2025-08-21 18:04:02] 更新2个订单状态为'待更新'
[2025-08-21 18:04:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:04:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:04:02] 最终队列长度: 7
[2025-08-21 18:04:02] 最终队列长度: 7
[2025-08-21 18:04:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:04:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:05:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:05:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:05:01] ✅ Redis连接成功
[2025-08-21 18:05:01] ✅ Redis连接成功
[2025-08-21 18:05:01] 当前队列长度: 0
[2025-08-21 18:05:01] 当前队列长度: 0
[2025-08-21 18:05:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 44.78%
[2025-08-21 18:05:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 44.78%
[2025-08-21 18:05:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:05:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:05:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:05:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:05:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:05:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:05:01] 更新2个订单状态为'待更新'
[2025-08-21 18:05:01] 更新2个订单状态为'待更新'
[2025-08-21 18:05:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:05:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:05:02] 最终队列长度: 2
[2025-08-21 18:05:02] 最终队列长度: 2
[2025-08-21 18:05:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:05:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:06:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:06:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:06:01] ✅ Redis连接成功
[2025-08-21 18:06:01] ✅ Redis连接成功
[2025-08-21 18:06:01] 当前队列长度: 4
[2025-08-21 18:06:01] 当前队列长度: 4
[2025-08-21 18:06:01] 跳过: 订单51已在队列中
[2025-08-21 18:06:01] 跳过: 订单51已在队列中
[2025-08-21 18:06:01] 跳过: 订单50已在队列中
[2025-08-21 18:06:01] 跳过: 订单50已在队列中
[2025-08-21 18:06:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:06:01] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:06:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:06:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:06:01] 最终队列长度: 4
[2025-08-21 18:06:01] 最终队列长度: 4
[2025-08-21 18:06:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:06:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:07:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:07:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:07:01] ✅ Redis连接成功
[2025-08-21 18:07:01] ✅ Redis连接成功
[2025-08-21 18:07:01] 当前队列长度: 0
[2025-08-21 18:07:01] 当前队列长度: 0
[2025-08-21 18:07:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 47.88%
[2025-08-21 18:07:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 47.88%
[2025-08-21 18:07:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:07:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:07:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:07:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:07:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:07:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:07:01] 更新2个订单状态为'待更新'
[2025-08-21 18:07:01] 更新2个订单状态为'待更新'
[2025-08-21 18:07:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:07:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:07:01] 最终队列长度: 2
[2025-08-21 18:07:01] 最终队列长度: 2
[2025-08-21 18:07:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:07:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:08:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:08:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:08:01] ✅ Redis连接成功
[2025-08-21 18:08:01] ✅ Redis连接成功
[2025-08-21 18:08:01] 当前队列长度: 0
[2025-08-21 18:08:01] 当前队列长度: 0
[2025-08-21 18:08:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 51.21%
[2025-08-21 18:08:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 51.21%
[2025-08-21 18:08:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:08:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:08:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:08:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:08:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:08:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:08:01] 更新2个订单状态为'待更新'
[2025-08-21 18:08:01] 更新2个订单状态为'待更新'
[2025-08-21 18:08:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:08:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:08:01] 最终队列长度: 4
[2025-08-21 18:08:01] 最终队列长度: 4
[2025-08-21 18:08:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:08:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:09:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:09:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:09:01] ✅ Redis连接成功
[2025-08-21 18:09:01] ✅ Redis连接成功
[2025-08-21 18:09:01] 当前队列长度: 0
[2025-08-21 18:09:01] 当前队列长度: 0
[2025-08-21 18:09:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 53.83%
[2025-08-21 18:09:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 53.83%
[2025-08-21 18:09:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:09:01] 准备入队: 订单50 - 622429199710014822 - 运行中 - 20%
[2025-08-21 18:09:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:09:01] 扫描到2个订单，其中2个需要快速入队
[2025-08-21 18:09:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:09:01] ✅ 成功快速入队2个新订单
[2025-08-21 18:09:01] 更新2个订单状态为'待更新'
[2025-08-21 18:09:01] 更新2个订单状态为'待更新'
[2025-08-21 18:09:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:09:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:09:01] 最终队列长度: 2
[2025-08-21 18:09:01] 最终队列长度: 2
[2025-08-21 18:09:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:09:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:10:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:10:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:10:02] ✅ Redis连接成功
[2025-08-21 18:10:02] ✅ Redis连接成功
[2025-08-21 18:10:02] 当前队列长度: 4
[2025-08-21 18:10:02] 当前队列长度: 4
[2025-08-21 18:10:02] 跳过: 订单51已在队列中
[2025-08-21 18:10:02] 跳过: 订单51已在队列中
[2025-08-21 18:10:02] 跳过: 订单50已在队列中
[2025-08-21 18:10:02] 跳过: 订单50已在队列中
[2025-08-21 18:10:02] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:10:02] 扫描到2个订单，其中0个需要快速入队
[2025-08-21 18:10:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:10:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:10:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:10:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:10:02] 最终队列长度: 4
[2025-08-21 18:10:02] 最终队列长度: 4
[2025-08-21 18:10:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:10:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:11:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:11:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:11:02] ✅ Redis连接成功
[2025-08-21 18:11:02] ✅ Redis连接成功
[2025-08-21 18:11:02] 当前队列长度: 0
[2025-08-21 18:11:02] 当前队列长度: 0
[2025-08-21 18:11:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 59.12%
[2025-08-21 18:11:02] 准备入队: 订单51 - 622429198901150742 - 进行中 - 59.12%
[2025-08-21 18:11:02] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:11:02] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:11:02] ✅ 成功快速入队1个新订单
[2025-08-21 18:11:02] ✅ 成功快速入队1个新订单
[2025-08-21 18:11:02] 更新1个订单状态为'待更新'
[2025-08-21 18:11:02] 更新1个订单状态为'待更新'
[2025-08-21 18:11:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:11:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:11:02] 最终队列长度: 1
[2025-08-21 18:11:02] 最终队列长度: 1
[2025-08-21 18:11:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:11:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:12:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:12:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:12:01] ✅ Redis连接成功
[2025-08-21 18:12:01] ✅ Redis连接成功
[2025-08-21 18:12:01] 当前队列长度: 0
[2025-08-21 18:12:01] 当前队列长度: 0
[2025-08-21 18:12:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 60.87%
[2025-08-21 18:12:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 60.87%
[2025-08-21 18:12:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:12:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:12:01] ✅ 成功快速入队1个新订单
[2025-08-21 18:12:01] ✅ 成功快速入队1个新订单
[2025-08-21 18:12:01] 更新1个订单状态为'待更新'
[2025-08-21 18:12:01] 更新1个订单状态为'待更新'
[2025-08-21 18:12:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:12:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:12:02] 最终队列长度: 4
[2025-08-21 18:12:02] 最终队列长度: 4
[2025-08-21 18:12:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:12:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:13:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:13:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:13:01] ✅ Redis连接成功
[2025-08-21 18:13:01] ✅ Redis连接成功
[2025-08-21 18:13:01] 当前队列长度: 0
[2025-08-21 18:13:01] 当前队列长度: 0
[2025-08-21 18:13:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 63.54%
[2025-08-21 18:13:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 63.54%
[2025-08-21 18:13:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:13:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:13:01] ✅ 成功快速入队1个新订单
[2025-08-21 18:13:01] ✅ 成功快速入队1个新订单
[2025-08-21 18:13:01] 更新1个订单状态为'待更新'
[2025-08-21 18:13:01] 更新1个订单状态为'待更新'
[2025-08-21 18:13:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:13:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:13:02] 最终队列长度: 1
[2025-08-21 18:13:02] 最终队列长度: 1
[2025-08-21 18:13:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:13:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:14:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:14:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:14:01] ✅ Redis连接成功
[2025-08-21 18:14:01] ✅ Redis连接成功
[2025-08-21 18:14:01] 当前队列长度: 4
[2025-08-21 18:14:01] 当前队列长度: 4
[2025-08-21 18:14:01] 跳过: 订单51已在队列中
[2025-08-21 18:14:01] 跳过: 订单51已在队列中
[2025-08-21 18:14:01] 扫描到1个订单，其中0个需要快速入队
[2025-08-21 18:14:01] 扫描到1个订单，其中0个需要快速入队
[2025-08-21 18:14:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:14:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:14:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:14:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:14:02] 最终队列长度: 4
[2025-08-21 18:14:02] 最终队列长度: 4
[2025-08-21 18:14:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:14:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:15:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:15:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:15:01] ✅ Redis连接成功
[2025-08-21 18:15:01] ✅ Redis连接成功
[2025-08-21 18:15:01] 当前队列长度: 0
[2025-08-21 18:15:01] 当前队列长度: 0
[2025-08-21 18:15:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 69.89%
[2025-08-21 18:15:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 69.89%
[2025-08-21 18:15:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:15:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 18:15:01] ✅ 成功快速入队1个新订单
[2025-08-21 18:15:01] ✅ 成功快速入队1个新订单
[2025-08-21 18:15:01] 更新1个订单状态为'待更新'
[2025-08-21 18:15:01] 更新1个订单状态为'待更新'
[2025-08-21 18:15:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:15:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:15:01] 最终队列长度: 1
[2025-08-21 18:15:01] 最终队列长度: 1
[2025-08-21 18:15:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:15:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:16:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:16:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:16:01] ✅ Redis连接成功
[2025-08-21 18:16:01] ✅ Redis连接成功
[2025-08-21 18:16:01] 当前队列长度: 4
[2025-08-21 18:16:01] 当前队列长度: 4
[2025-08-21 18:16:01] 跳过: 订单51已在队列中
[2025-08-21 18:16:01] 跳过: 订单51已在队列中
[2025-08-21 18:16:01] 扫描到1个订单，其中0个需要快速入队
[2025-08-21 18:16:01] 扫描到1个订单，其中0个需要快速入队
[2025-08-21 18:16:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:16:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:16:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:16:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:16:01] 最终队列长度: 4
[2025-08-21 18:16:01] 最终队列长度: 4
[2025-08-21 18:16:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:16:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:17:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:17:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:17:01] ✅ Redis连接成功
[2025-08-21 18:17:01] ✅ Redis连接成功
[2025-08-21 18:17:01] 当前队列长度: 0
[2025-08-21 18:17:01] 当前队列长度: 0
[2025-08-21 18:17:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:17:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:17:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:17:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:17:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:17:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:17:01] 最终队列长度: 0
[2025-08-21 18:17:01] 最终队列长度: 0
[2025-08-21 18:17:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:17:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:18:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:18:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:18:01] ✅ Redis连接成功
[2025-08-21 18:18:01] ✅ Redis连接成功
[2025-08-21 18:18:01] 当前队列长度: 0
[2025-08-21 18:18:01] 当前队列长度: 0
[2025-08-21 18:18:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:18:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:18:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:18:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:18:01] 最终队列长度: 4
[2025-08-21 18:18:01] 最终队列长度: 4
[2025-08-21 18:18:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:18:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:19:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:19:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:19:01] ✅ Redis连接成功
[2025-08-21 18:19:01] ✅ Redis连接成功
[2025-08-21 18:19:01] 当前队列长度: 0
[2025-08-21 18:19:01] 当前队列长度: 0
[2025-08-21 18:19:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:19:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:19:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:19:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:19:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:19:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:19:01] 最终队列长度: 0
[2025-08-21 18:19:01] 最终队列长度: 0
[2025-08-21 18:19:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:19:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:20:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:20:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:20:01] ✅ Redis连接成功
[2025-08-21 18:20:01] ✅ Redis连接成功
[2025-08-21 18:20:01] 当前队列长度: 0
[2025-08-21 18:20:01] 当前队列长度: 0
[2025-08-21 18:20:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:20:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:20:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:20:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:20:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:20:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:20:01] 最终队列长度: 4
[2025-08-21 18:20:01] 最终队列长度: 4
[2025-08-21 18:20:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:20:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:21:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:21:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:21:01] ✅ Redis连接成功
[2025-08-21 18:21:01] ✅ Redis连接成功
[2025-08-21 18:21:01] 当前队列长度: 0
[2025-08-21 18:21:01] 当前队列长度: 0
[2025-08-21 18:21:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:21:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:21:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:21:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:21:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:21:01] 最终队列长度: 0
[2025-08-21 18:21:01] 最终队列长度: 0
[2025-08-21 18:21:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:21:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:22:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:22:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:22:01] ✅ Redis连接成功
[2025-08-21 18:22:01] ✅ Redis连接成功
[2025-08-21 18:22:01] 当前队列长度: 4
[2025-08-21 18:22:01] 当前队列长度: 4
[2025-08-21 18:22:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:22:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:22:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:22:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:22:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:22:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:22:01] 最终队列长度: 4
[2025-08-21 18:22:01] 最终队列长度: 4
[2025-08-21 18:22:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:22:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:23:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:23:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:23:01] ✅ Redis连接成功
[2025-08-21 18:23:01] ✅ Redis连接成功
[2025-08-21 18:23:01] 当前队列长度: 0
[2025-08-21 18:23:01] 当前队列长度: 0
[2025-08-21 18:23:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:23:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:23:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:23:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:23:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:23:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:23:01] 最终队列长度: 0
[2025-08-21 18:23:01] 最终队列长度: 0
[2025-08-21 18:23:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:23:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:24:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:24:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:24:02] ✅ Redis连接成功
[2025-08-21 18:24:02] ✅ Redis连接成功
[2025-08-21 18:24:02] 当前队列长度: 0
[2025-08-21 18:24:02] 当前队列长度: 0
[2025-08-21 18:24:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:24:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:24:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:24:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:24:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:24:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:24:02] 最终队列长度: 4
[2025-08-21 18:24:02] 最终队列长度: 4
[2025-08-21 18:24:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:24:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:25:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:25:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:25:01] ✅ Redis连接成功
[2025-08-21 18:25:01] ✅ Redis连接成功
[2025-08-21 18:25:01] 当前队列长度: 0
[2025-08-21 18:25:01] 当前队列长度: 0
[2025-08-21 18:25:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:25:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:25:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:25:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:25:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:25:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:25:02] 最终队列长度: 0
[2025-08-21 18:25:02] 最终队列长度: 0
[2025-08-21 18:25:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:25:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:26:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:26:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:26:01] ✅ Redis连接成功
[2025-08-21 18:26:01] ✅ Redis连接成功
[2025-08-21 18:26:01] 当前队列长度: 4
[2025-08-21 18:26:01] 当前队列长度: 4
[2025-08-21 18:26:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:26:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:26:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:26:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:26:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:26:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:26:02] 最终队列长度: 4
[2025-08-21 18:26:02] 最终队列长度: 4
[2025-08-21 18:26:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:26:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:27:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:27:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:27:01] ✅ Redis连接成功
[2025-08-21 18:27:01] ✅ Redis连接成功
[2025-08-21 18:27:01] 当前队列长度: 0
[2025-08-21 18:27:01] 当前队列长度: 0
[2025-08-21 18:27:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:27:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:27:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:27:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:27:02] 最终队列长度: 0
[2025-08-21 18:27:02] 最终队列长度: 0
[2025-08-21 18:27:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:27:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:28:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:28:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:28:01] ✅ Redis连接成功
[2025-08-21 18:28:01] ✅ Redis连接成功
[2025-08-21 18:28:01] 当前队列长度: 0
[2025-08-21 18:28:01] 当前队列长度: 0
[2025-08-21 18:28:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:28:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:28:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:28:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:28:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:28:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:28:01] 最终队列长度: 4
[2025-08-21 18:28:01] 最终队列长度: 4
[2025-08-21 18:28:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:28:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:29:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:29:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:29:01] ✅ Redis连接成功
[2025-08-21 18:29:01] ✅ Redis连接成功
[2025-08-21 18:29:01] 当前队列长度: 0
[2025-08-21 18:29:01] 当前队列长度: 0
[2025-08-21 18:29:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:29:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:29:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:29:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:29:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:29:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:29:01] 最终队列长度: 0
[2025-08-21 18:29:01] 最终队列长度: 0
[2025-08-21 18:29:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:29:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:30:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:30:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:30:01] ✅ Redis连接成功
[2025-08-21 18:30:01] ✅ Redis连接成功
[2025-08-21 18:30:01] 当前队列长度: 0
[2025-08-21 18:30:01] 当前队列长度: 0
[2025-08-21 18:30:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:30:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:30:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:30:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:30:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:30:01] 最终队列长度: 4
[2025-08-21 18:30:01] 最终队列长度: 4
[2025-08-21 18:30:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:30:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:31:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:31:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:31:01] ✅ Redis连接成功
[2025-08-21 18:31:01] ✅ Redis连接成功
[2025-08-21 18:31:01] 当前队列长度: 0
[2025-08-21 18:31:01] 当前队列长度: 0
[2025-08-21 18:31:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:31:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:31:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:31:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:31:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:31:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:31:01] 最终队列长度: 0
[2025-08-21 18:31:01] 最终队列长度: 0
[2025-08-21 18:31:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:31:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:32:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:32:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:32:02] ✅ Redis连接成功
[2025-08-21 18:32:02] ✅ Redis连接成功
[2025-08-21 18:32:02] 当前队列长度: 0
[2025-08-21 18:32:02] 当前队列长度: 0
[2025-08-21 18:32:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:32:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:32:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:32:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:32:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:32:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:32:02] 最终队列长度: 4
[2025-08-21 18:32:02] 最终队列长度: 4
[2025-08-21 18:32:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:32:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:33:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:33:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:33:01] ✅ Redis连接成功
[2025-08-21 18:33:01] ✅ Redis连接成功
[2025-08-21 18:33:01] 当前队列长度: 0
[2025-08-21 18:33:01] 当前队列长度: 0
[2025-08-21 18:33:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:33:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:33:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:33:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:33:02] 最终队列长度: 0
[2025-08-21 18:33:02] 最终队列长度: 0
[2025-08-21 18:33:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:33:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:34:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:34:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:34:01] ✅ Redis连接成功
[2025-08-21 18:34:01] ✅ Redis连接成功
[2025-08-21 18:34:01] 当前队列长度: 0
[2025-08-21 18:34:01] 当前队列长度: 0
[2025-08-21 18:34:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:34:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:34:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:34:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:34:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:34:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:34:01] 最终队列长度: 4
[2025-08-21 18:34:01] 最终队列长度: 4
[2025-08-21 18:34:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:34:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:36:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:36:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:36:01] ✅ Redis连接成功
[2025-08-21 18:36:01] ✅ Redis连接成功
[2025-08-21 18:36:01] 当前队列长度: 4
[2025-08-21 18:36:01] 当前队列长度: 4
[2025-08-21 18:36:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:36:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:36:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:36:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:36:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:36:01] 最终队列长度: 4
[2025-08-21 18:36:01] 最终队列长度: 4
[2025-08-21 18:36:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:36:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:37:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:37:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:37:01] ✅ Redis连接成功
[2025-08-21 18:37:01] ✅ Redis连接成功
[2025-08-21 18:37:01] 当前队列长度: 0
[2025-08-21 18:37:01] 当前队列长度: 0
[2025-08-21 18:37:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:37:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:37:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:37:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:37:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:37:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:37:01] 最终队列长度: 0
[2025-08-21 18:37:01] 最终队列长度: 0
[2025-08-21 18:37:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:37:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:38:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:38:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:38:01] ✅ Redis连接成功
[2025-08-21 18:38:01] ✅ Redis连接成功
[2025-08-21 18:38:01] 当前队列长度: 4
[2025-08-21 18:38:01] 当前队列长度: 4
[2025-08-21 18:38:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:38:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:38:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:38:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:38:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:38:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:38:01] 最终队列长度: 4
[2025-08-21 18:38:01] 最终队列长度: 4
[2025-08-21 18:38:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:38:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:39:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:39:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:39:01] ✅ Redis连接成功
[2025-08-21 18:39:01] ✅ Redis连接成功
[2025-08-21 18:39:01] 当前队列长度: 0
[2025-08-21 18:39:01] 当前队列长度: 0
[2025-08-21 18:39:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:39:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:39:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:39:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:39:01] 最终队列长度: 0
[2025-08-21 18:39:01] 最终队列长度: 0
[2025-08-21 18:39:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:39:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:40:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:40:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:40:01] ✅ Redis连接成功
[2025-08-21 18:40:01] ✅ Redis连接成功
[2025-08-21 18:40:01] 当前队列长度: 3
[2025-08-21 18:40:01] 当前队列长度: 3
[2025-08-21 18:40:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:40:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:40:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:40:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:40:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:40:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:40:01] 最终队列长度: 3
[2025-08-21 18:40:01] 最终队列长度: 3
[2025-08-21 18:40:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:40:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:41:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:41:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:41:01] ✅ Redis连接成功
[2025-08-21 18:41:01] ✅ Redis连接成功
[2025-08-21 18:41:01] 当前队列长度: 0
[2025-08-21 18:41:01] 当前队列长度: 0
[2025-08-21 18:41:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:41:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:41:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:41:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:41:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:41:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:41:01] 最终队列长度: 0
[2025-08-21 18:41:01] 最终队列长度: 0
[2025-08-21 18:41:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:41:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:42:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:42:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:42:01] ✅ Redis连接成功
[2025-08-21 18:42:01] ✅ Redis连接成功
[2025-08-21 18:42:01] 当前队列长度: 0
[2025-08-21 18:42:01] 当前队列长度: 0
[2025-08-21 18:42:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:42:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:42:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:42:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:42:01] 最终队列长度: 3
[2025-08-21 18:42:01] 最终队列长度: 3
[2025-08-21 18:42:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:42:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:43:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:43:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:43:01] ✅ Redis连接成功
[2025-08-21 18:43:01] ✅ Redis连接成功
[2025-08-21 18:43:01] 当前队列长度: 0
[2025-08-21 18:43:01] 当前队列长度: 0
[2025-08-21 18:43:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:43:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:43:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:43:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:43:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:43:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:43:01] 最终队列长度: 0
[2025-08-21 18:43:01] 最终队列长度: 0
[2025-08-21 18:43:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:43:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:44:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:44:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:44:01] ✅ Redis连接成功
[2025-08-21 18:44:01] ✅ Redis连接成功
[2025-08-21 18:44:01] 当前队列长度: 0
[2025-08-21 18:44:01] 当前队列长度: 0
[2025-08-21 18:44:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:44:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:44:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:44:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:44:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:44:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:44:01] 最终队列长度: 3
[2025-08-21 18:44:01] 最终队列长度: 3
[2025-08-21 18:44:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:44:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:45:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:45:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:45:01] ✅ Redis连接成功
[2025-08-21 18:45:01] ✅ Redis连接成功
[2025-08-21 18:45:01] 当前队列长度: 0
[2025-08-21 18:45:01] 当前队列长度: 0
[2025-08-21 18:45:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:45:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:45:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:45:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:45:01] 最终队列长度: 0
[2025-08-21 18:45:01] 最终队列长度: 0
[2025-08-21 18:45:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:45:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:46:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:46:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:46:01] ✅ Redis连接成功
[2025-08-21 18:46:01] ✅ Redis连接成功
[2025-08-21 18:46:01] 当前队列长度: 0
[2025-08-21 18:46:01] 当前队列长度: 0
[2025-08-21 18:46:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:46:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:46:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:46:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:46:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:46:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:46:02] 最终队列长度: 3
[2025-08-21 18:46:02] 最终队列长度: 3
[2025-08-21 18:46:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:46:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:47:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:47:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:47:01] ✅ Redis连接成功
[2025-08-21 18:47:01] ✅ Redis连接成功
[2025-08-21 18:47:01] 当前队列长度: 0
[2025-08-21 18:47:01] 当前队列长度: 0
[2025-08-21 18:47:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:47:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:47:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:47:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:47:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:47:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:47:02] 最终队列长度: 0
[2025-08-21 18:47:02] 最终队列长度: 0
[2025-08-21 18:47:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:47:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:48:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:48:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:48:02] ✅ Redis连接成功
[2025-08-21 18:48:02] ✅ Redis连接成功
[2025-08-21 18:48:02] 当前队列长度: 3
[2025-08-21 18:48:02] 当前队列长度: 3
[2025-08-21 18:48:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:48:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:48:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:48:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:48:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:48:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:48:02] 最终队列长度: 3
[2025-08-21 18:48:02] 最终队列长度: 3
[2025-08-21 18:48:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:48:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:49:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:49:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:49:01] ✅ Redis连接成功
[2025-08-21 18:49:01] ✅ Redis连接成功
[2025-08-21 18:49:01] 当前队列长度: 0
[2025-08-21 18:49:01] 当前队列长度: 0
[2025-08-21 18:49:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:49:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:49:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:49:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:49:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:49:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:49:01] 最终队列长度: 0
[2025-08-21 18:49:01] 最终队列长度: 0
[2025-08-21 18:49:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:49:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:50:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:50:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:50:01] ✅ Redis连接成功
[2025-08-21 18:50:01] ✅ Redis连接成功
[2025-08-21 18:50:01] 当前队列长度: 3
[2025-08-21 18:50:01] 当前队列长度: 3
[2025-08-21 18:50:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:50:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:50:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:50:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:50:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:50:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:50:01] 最终队列长度: 3
[2025-08-21 18:50:01] 最终队列长度: 3
[2025-08-21 18:50:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:50:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:51:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:51:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:51:01] ✅ Redis连接成功
[2025-08-21 18:51:01] ✅ Redis连接成功
[2025-08-21 18:51:01] 当前队列长度: 0
[2025-08-21 18:51:01] 当前队列长度: 0
[2025-08-21 18:51:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:51:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:51:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:51:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:51:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:51:01] 最终队列长度: 0
[2025-08-21 18:51:01] 最终队列长度: 0
[2025-08-21 18:51:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:51:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:52:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:52:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:52:01] ✅ Redis连接成功
[2025-08-21 18:52:01] ✅ Redis连接成功
[2025-08-21 18:52:01] 当前队列长度: 3
[2025-08-21 18:52:01] 当前队列长度: 3
[2025-08-21 18:52:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:52:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:52:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:52:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:52:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:52:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:52:01] 最终队列长度: 3
[2025-08-21 18:52:01] 最终队列长度: 3
[2025-08-21 18:52:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:52:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:53:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:53:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:53:01] ✅ Redis连接成功
[2025-08-21 18:53:01] ✅ Redis连接成功
[2025-08-21 18:53:01] 当前队列长度: 0
[2025-08-21 18:53:01] 当前队列长度: 0
[2025-08-21 18:53:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:53:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:53:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:53:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:53:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:53:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:53:01] 最终队列长度: 0
[2025-08-21 18:53:01] 最终队列长度: 0
[2025-08-21 18:53:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:53:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:54:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:54:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:54:01] ✅ Redis连接成功
[2025-08-21 18:54:01] ✅ Redis连接成功
[2025-08-21 18:54:01] 当前队列长度: 0
[2025-08-21 18:54:01] 当前队列长度: 0
[2025-08-21 18:54:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:54:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:54:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:54:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:54:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:54:02] 最终队列长度: 3
[2025-08-21 18:54:02] 最终队列长度: 3
[2025-08-21 18:54:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:54:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:55:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:55:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:55:01] ✅ Redis连接成功
[2025-08-21 18:55:01] ✅ Redis连接成功
[2025-08-21 18:55:01] 当前队列长度: 0
[2025-08-21 18:55:01] 当前队列长度: 0
[2025-08-21 18:55:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:55:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:55:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:55:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:55:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:55:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:55:02] 最终队列长度: 0
[2025-08-21 18:55:02] 最终队列长度: 0
[2025-08-21 18:55:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:55:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:56:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:56:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:56:01] ✅ Redis连接成功
[2025-08-21 18:56:01] ✅ Redis连接成功
[2025-08-21 18:56:01] 当前队列长度: 3
[2025-08-21 18:56:01] 当前队列长度: 3
[2025-08-21 18:56:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:56:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:56:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:56:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:56:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:56:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:56:01] 最终队列长度: 3
[2025-08-21 18:56:01] 最终队列长度: 3
[2025-08-21 18:56:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:56:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:57:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:57:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:57:01] ✅ Redis连接成功
[2025-08-21 18:57:01] ✅ Redis连接成功
[2025-08-21 18:57:01] 当前队列长度: 0
[2025-08-21 18:57:01] 当前队列长度: 0
[2025-08-21 18:57:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:57:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:57:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:57:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:57:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:57:01] 最终队列长度: 0
[2025-08-21 18:57:01] 最终队列长度: 0
[2025-08-21 18:57:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:57:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:58:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:58:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:58:01] ✅ Redis连接成功
[2025-08-21 18:58:01] ✅ Redis连接成功
[2025-08-21 18:58:01] 当前队列长度: 3
[2025-08-21 18:58:01] 当前队列长度: 3
[2025-08-21 18:58:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:58:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:58:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:58:01] ℹ️ 没有新订单需要入队
[2025-08-21 18:58:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:58:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:58:01] 最终队列长度: 3
[2025-08-21 18:58:01] 最终队列长度: 3
[2025-08-21 18:58:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:58:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:59:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:59:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 18:59:02] ✅ Redis连接成功
[2025-08-21 18:59:02] ✅ Redis连接成功
[2025-08-21 18:59:02] 当前队列长度: 0
[2025-08-21 18:59:02] 当前队列长度: 0
[2025-08-21 18:59:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:59:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 18:59:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:59:02] ℹ️ 没有新订单需要入队
[2025-08-21 18:59:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:59:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 18:59:02] 最终队列长度: 0
[2025-08-21 18:59:02] 最终队列长度: 0
[2025-08-21 18:59:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 18:59:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:00:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:00:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:00:02] ✅ Redis连接成功
[2025-08-21 19:00:02] ✅ Redis连接成功
[2025-08-21 19:00:02] 当前队列长度: 3
[2025-08-21 19:00:02] 当前队列长度: 3
[2025-08-21 19:00:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:00:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:00:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:00:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:00:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:00:02] 最终队列长度: 3
[2025-08-21 19:00:02] 最终队列长度: 3
[2025-08-21 19:00:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:00:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:01:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:01:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:01:01] ✅ Redis连接成功
[2025-08-21 19:01:01] ✅ Redis连接成功
[2025-08-21 19:01:01] 当前队列长度: 0
[2025-08-21 19:01:01] 当前队列长度: 0
[2025-08-21 19:01:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:01:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:01:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:01:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:01:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:01:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:01:02] 最终队列长度: 0
[2025-08-21 19:01:02] 最终队列长度: 0
[2025-08-21 19:01:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:01:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:02:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:02:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:02:01] ✅ Redis连接成功
[2025-08-21 19:02:01] ✅ Redis连接成功
[2025-08-21 19:02:01] 当前队列长度: 0
[2025-08-21 19:02:01] 当前队列长度: 0
[2025-08-21 19:02:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:02:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:02:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:02:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:02:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:02:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:02:02] 最终队列长度: 3
[2025-08-21 19:02:02] 最终队列长度: 3
[2025-08-21 19:02:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:02:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:03:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:03:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:03:01] ✅ Redis连接成功
[2025-08-21 19:03:01] ✅ Redis连接成功
[2025-08-21 19:03:01] 当前队列长度: 0
[2025-08-21 19:03:01] 当前队列长度: 0
[2025-08-21 19:03:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:03:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:03:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:03:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:03:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:03:02] 最终队列长度: 0
[2025-08-21 19:03:02] 最终队列长度: 0
[2025-08-21 19:03:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:03:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:04:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:04:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:04:01] ✅ Redis连接成功
[2025-08-21 19:04:01] ✅ Redis连接成功
[2025-08-21 19:04:01] 当前队列长度: 3
[2025-08-21 19:04:01] 当前队列长度: 3
[2025-08-21 19:04:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:04:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:04:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:04:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:04:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:04:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:04:01] 最终队列长度: 3
[2025-08-21 19:04:01] 最终队列长度: 3
[2025-08-21 19:04:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:04:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:05:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:05:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:05:01] ✅ Redis连接成功
[2025-08-21 19:05:01] ✅ Redis连接成功
[2025-08-21 19:05:01] 当前队列长度: 0
[2025-08-21 19:05:01] 当前队列长度: 0
[2025-08-21 19:05:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:05:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:05:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:05:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:05:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:05:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:05:01] 最终队列长度: 0
[2025-08-21 19:05:01] 最终队列长度: 0
[2025-08-21 19:05:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:05:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:06:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:06:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:06:01] ✅ Redis连接成功
[2025-08-21 19:06:01] ✅ Redis连接成功
[2025-08-21 19:06:01] 当前队列长度: 0
[2025-08-21 19:06:01] 当前队列长度: 0
[2025-08-21 19:06:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:06:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:06:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:06:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:06:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:06:01] 最终队列长度: 3
[2025-08-21 19:06:01] 最终队列长度: 3
[2025-08-21 19:06:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:06:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:07:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:07:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:07:01] ✅ Redis连接成功
[2025-08-21 19:07:01] ✅ Redis连接成功
[2025-08-21 19:07:01] 当前队列长度: 0
[2025-08-21 19:07:01] 当前队列长度: 0
[2025-08-21 19:07:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:07:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:07:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:07:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:07:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:07:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:07:01] 最终队列长度: 0
[2025-08-21 19:07:01] 最终队列长度: 0
[2025-08-21 19:07:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:07:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:08:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:08:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:08:02] ✅ Redis连接成功
[2025-08-21 19:08:02] ✅ Redis连接成功
[2025-08-21 19:08:02] 当前队列长度: 3
[2025-08-21 19:08:02] 当前队列长度: 3
[2025-08-21 19:08:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:08:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:08:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:08:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:08:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:08:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:08:02] 最终队列长度: 3
[2025-08-21 19:08:02] 最终队列长度: 3
[2025-08-21 19:08:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:08:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:09:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:09:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:09:01] ✅ Redis连接成功
[2025-08-21 19:09:01] ✅ Redis连接成功
[2025-08-21 19:09:01] 当前队列长度: 0
[2025-08-21 19:09:01] 当前队列长度: 0
[2025-08-21 19:09:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:09:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:09:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:09:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:09:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:09:02] 最终队列长度: 0
[2025-08-21 19:09:02] 最终队列长度: 0
[2025-08-21 19:09:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:09:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:10:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:10:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:10:01] ✅ Redis连接成功
[2025-08-21 19:10:01] ✅ Redis连接成功
[2025-08-21 19:10:01] 当前队列长度: 0
[2025-08-21 19:10:01] 当前队列长度: 0
[2025-08-21 19:10:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:10:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:10:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:10:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:10:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:10:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:10:02] 最终队列长度: 3
[2025-08-21 19:10:02] 最终队列长度: 3
[2025-08-21 19:10:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:10:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:11:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:11:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:11:01] ✅ Redis连接成功
[2025-08-21 19:11:01] ✅ Redis连接成功
[2025-08-21 19:11:01] 当前队列长度: 0
[2025-08-21 19:11:01] 当前队列长度: 0
[2025-08-21 19:11:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:11:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:11:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:11:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:11:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:11:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:11:01] 最终队列长度: 0
[2025-08-21 19:11:01] 最终队列长度: 0
[2025-08-21 19:11:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:11:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:12:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:12:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:12:01] ✅ Redis连接成功
[2025-08-21 19:12:01] ✅ Redis连接成功
[2025-08-21 19:12:01] 当前队列长度: 0
[2025-08-21 19:12:01] 当前队列长度: 0
[2025-08-21 19:12:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:12:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:12:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:12:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:12:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:12:01] 最终队列长度: 0
[2025-08-21 19:12:01] 最终队列长度: 0
[2025-08-21 19:12:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:12:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:13:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:13:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:13:01] ✅ Redis连接成功
[2025-08-21 19:13:01] ✅ Redis连接成功
[2025-08-21 19:13:01] 当前队列长度: 0
[2025-08-21 19:13:01] 当前队列长度: 0
[2025-08-21 19:13:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:13:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:13:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:13:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:13:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:13:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:13:01] 最终队列长度: 0
[2025-08-21 19:13:01] 最终队列长度: 0
[2025-08-21 19:13:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:13:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:14:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:14:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:14:01] ✅ Redis连接成功
[2025-08-21 19:14:01] ✅ Redis连接成功
[2025-08-21 19:14:01] 当前队列长度: 0
[2025-08-21 19:14:01] 当前队列长度: 0
[2025-08-21 19:14:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:14:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:14:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:14:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:14:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:14:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:14:01] 最终队列长度: 3
[2025-08-21 19:14:01] 最终队列长度: 3
[2025-08-21 19:14:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:14:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:15:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:15:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:15:01] ✅ Redis连接成功
[2025-08-21 19:15:01] ✅ Redis连接成功
[2025-08-21 19:15:01] 当前队列长度: 0
[2025-08-21 19:15:01] 当前队列长度: 0
[2025-08-21 19:15:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:15:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:15:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:15:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:15:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:15:01] 最终队列长度: 0
[2025-08-21 19:15:01] 最终队列长度: 0
[2025-08-21 19:15:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:15:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:16:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:16:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:16:01] ✅ Redis连接成功
[2025-08-21 19:16:01] ✅ Redis连接成功
[2025-08-21 19:16:01] 当前队列长度: 3
[2025-08-21 19:16:01] 当前队列长度: 3
[2025-08-21 19:16:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:16:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:16:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:16:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:16:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:16:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:16:02] 最终队列长度: 3
[2025-08-21 19:16:02] 最终队列长度: 3
[2025-08-21 19:16:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:16:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:17:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:17:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:17:01] ✅ Redis连接成功
[2025-08-21 19:17:01] ✅ Redis连接成功
[2025-08-21 19:17:01] 当前队列长度: 0
[2025-08-21 19:17:01] 当前队列长度: 0
[2025-08-21 19:17:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:17:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:17:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:17:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:17:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:17:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:17:01] 最终队列长度: 0
[2025-08-21 19:17:01] 最终队列长度: 0
[2025-08-21 19:17:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:17:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:18:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:18:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:18:01] ✅ Redis连接成功
[2025-08-21 19:18:01] ✅ Redis连接成功
[2025-08-21 19:18:01] 当前队列长度: 3
[2025-08-21 19:18:01] 当前队列长度: 3
[2025-08-21 19:18:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:18:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:18:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:18:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:18:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:18:01] 最终队列长度: 3
[2025-08-21 19:18:01] 最终队列长度: 3
[2025-08-21 19:18:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:18:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:19:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:19:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:19:01] ✅ Redis连接成功
[2025-08-21 19:19:01] ✅ Redis连接成功
[2025-08-21 19:19:01] 当前队列长度: 0
[2025-08-21 19:19:01] 当前队列长度: 0
[2025-08-21 19:19:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:19:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:19:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:19:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:19:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:19:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:19:01] 最终队列长度: 0
[2025-08-21 19:19:01] 最终队列长度: 0
[2025-08-21 19:19:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:19:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:20:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:20:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:20:01] ✅ Redis连接成功
[2025-08-21 19:20:01] ✅ Redis连接成功
[2025-08-21 19:20:01] 当前队列长度: 3
[2025-08-21 19:20:01] 当前队列长度: 3
[2025-08-21 19:20:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:20:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:20:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:20:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:20:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:20:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:20:01] 最终队列长度: 3
[2025-08-21 19:20:01] 最终队列长度: 3
[2025-08-21 19:20:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:20:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:21:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:21:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:21:02] ✅ Redis连接成功
[2025-08-21 19:21:02] ✅ Redis连接成功
[2025-08-21 19:21:02] 当前队列长度: 0
[2025-08-21 19:21:02] 当前队列长度: 0
[2025-08-21 19:21:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:21:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:21:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:21:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:21:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:21:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:21:02] 最终队列长度: 0
[2025-08-21 19:21:02] 最终队列长度: 0
[2025-08-21 19:21:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:21:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:22:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:22:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:22:02] ✅ Redis连接成功
[2025-08-21 19:22:02] ✅ Redis连接成功
[2025-08-21 19:22:02] 当前队列长度: 0
[2025-08-21 19:22:02] 当前队列长度: 0
[2025-08-21 19:22:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:22:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:22:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:22:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:22:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:22:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:22:02] 最终队列长度: 3
[2025-08-21 19:22:02] 最终队列长度: 3
[2025-08-21 19:22:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:22:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:23:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:23:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:23:01] ✅ Redis连接成功
[2025-08-21 19:23:01] ✅ Redis连接成功
[2025-08-21 19:23:01] 当前队列长度: 0
[2025-08-21 19:23:01] 当前队列长度: 0
[2025-08-21 19:23:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:23:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:23:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:23:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:23:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:23:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:23:02] 最终队列长度: 0
[2025-08-21 19:23:02] 最终队列长度: 0
[2025-08-21 19:23:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:23:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:24:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:24:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:24:01] ✅ Redis连接成功
[2025-08-21 19:24:01] ✅ Redis连接成功
[2025-08-21 19:24:01] 当前队列长度: 3
[2025-08-21 19:24:01] 当前队列长度: 3
[2025-08-21 19:24:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:24:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:24:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:24:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:24:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:24:02] 最终队列长度: 3
[2025-08-21 19:24:02] 最终队列长度: 3
[2025-08-21 19:24:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:24:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:25:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:25:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:25:01] ✅ Redis连接成功
[2025-08-21 19:25:01] ✅ Redis连接成功
[2025-08-21 19:25:01] 当前队列长度: 0
[2025-08-21 19:25:01] 当前队列长度: 0
[2025-08-21 19:25:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:25:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:25:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:25:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:25:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:25:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:25:01] 最终队列长度: 0
[2025-08-21 19:25:01] 最终队列长度: 0
[2025-08-21 19:25:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:25:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:26:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:26:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:26:01] ✅ Redis连接成功
[2025-08-21 19:26:01] ✅ Redis连接成功
[2025-08-21 19:26:01] 当前队列长度: 3
[2025-08-21 19:26:01] 当前队列长度: 3
[2025-08-21 19:26:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:26:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:26:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:26:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:26:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:26:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:26:01] 最终队列长度: 3
[2025-08-21 19:26:01] 最终队列长度: 3
[2025-08-21 19:26:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:26:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:27:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:27:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:27:01] ✅ Redis连接成功
[2025-08-21 19:27:01] ✅ Redis连接成功
[2025-08-21 19:27:01] 当前队列长度: 0
[2025-08-21 19:27:01] 当前队列长度: 0
[2025-08-21 19:27:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:27:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:27:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:27:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:27:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:27:01] 最终队列长度: 0
[2025-08-21 19:27:01] 最终队列长度: 0
[2025-08-21 19:27:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:27:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:28:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:28:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:28:01] ✅ Redis连接成功
[2025-08-21 19:28:01] ✅ Redis连接成功
[2025-08-21 19:28:01] 当前队列长度: 0
[2025-08-21 19:28:01] 当前队列长度: 0
[2025-08-21 19:28:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:28:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:28:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:28:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:28:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:28:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:28:01] 最终队列长度: 3
[2025-08-21 19:28:01] 最终队列长度: 3
[2025-08-21 19:28:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:28:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:29:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:29:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:29:01] ✅ Redis连接成功
[2025-08-21 19:29:01] ✅ Redis连接成功
[2025-08-21 19:29:01] 当前队列长度: 0
[2025-08-21 19:29:01] 当前队列长度: 0
[2025-08-21 19:29:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:29:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:29:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:29:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:29:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:29:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:29:01] 最终队列长度: 0
[2025-08-21 19:29:01] 最终队列长度: 0
[2025-08-21 19:29:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:29:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:30:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:30:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:30:02] ✅ Redis连接成功
[2025-08-21 19:30:02] ✅ Redis连接成功
[2025-08-21 19:30:02] 当前队列长度: 0
[2025-08-21 19:30:02] 当前队列长度: 0
[2025-08-21 19:30:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:30:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:30:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:30:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:30:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:30:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:30:02] 最终队列长度: 3
[2025-08-21 19:30:02] 最终队列长度: 3
[2025-08-21 19:30:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:30:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:31:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:31:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:31:01] ✅ Redis连接成功
[2025-08-21 19:31:01] ✅ Redis连接成功
[2025-08-21 19:31:01] 当前队列长度: 0
[2025-08-21 19:31:01] 当前队列长度: 0
[2025-08-21 19:31:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:31:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:31:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:31:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:31:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:31:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:31:02] 最终队列长度: 0
[2025-08-21 19:31:02] 最终队列长度: 0
[2025-08-21 19:31:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:31:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:32:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:32:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:32:01] ✅ Redis连接成功
[2025-08-21 19:32:01] ✅ Redis连接成功
[2025-08-21 19:32:01] 当前队列长度: 0
[2025-08-21 19:32:01] 当前队列长度: 0
[2025-08-21 19:32:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:32:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:32:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:32:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:32:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:32:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:32:02] 最终队列长度: 3
[2025-08-21 19:32:02] 最终队列长度: 3
[2025-08-21 19:32:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:32:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:33:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:33:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:33:01] ✅ Redis连接成功
[2025-08-21 19:33:01] ✅ Redis连接成功
[2025-08-21 19:33:01] 当前队列长度: 0
[2025-08-21 19:33:01] 当前队列长度: 0
[2025-08-21 19:33:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:33:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:33:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:33:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:33:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:33:01] 最终队列长度: 0
[2025-08-21 19:33:01] 最终队列长度: 0
[2025-08-21 19:33:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:33:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:34:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:34:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:34:01] ✅ Redis连接成功
[2025-08-21 19:34:01] ✅ Redis连接成功
[2025-08-21 19:34:01] 当前队列长度: 0
[2025-08-21 19:34:01] 当前队列长度: 0
[2025-08-21 19:34:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:34:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:34:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:34:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:34:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:34:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:34:01] 最终队列长度: 3
[2025-08-21 19:34:01] 最终队列长度: 3
[2025-08-21 19:34:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:34:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:35:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:35:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:35:01] ✅ Redis连接成功
[2025-08-21 19:35:01] ✅ Redis连接成功
[2025-08-21 19:35:01] 当前队列长度: 0
[2025-08-21 19:35:01] 当前队列长度: 0
[2025-08-21 19:35:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:35:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:35:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:35:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:35:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:35:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:35:01] 最终队列长度: 0
[2025-08-21 19:35:01] 最终队列长度: 0
[2025-08-21 19:35:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:35:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:36:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:36:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:36:02] ✅ Redis连接成功
[2025-08-21 19:36:02] ✅ Redis连接成功
[2025-08-21 19:36:02] 当前队列长度: 3
[2025-08-21 19:36:02] 当前队列长度: 3
[2025-08-21 19:36:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:36:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:36:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:36:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:36:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:36:02] 最终队列长度: 3
[2025-08-21 19:36:02] 最终队列长度: 3
[2025-08-21 19:36:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:36:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:37:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:37:02] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:37:02] ✅ Redis连接成功
[2025-08-21 19:37:02] ✅ Redis连接成功
[2025-08-21 19:37:02] 当前队列长度: 0
[2025-08-21 19:37:02] 当前队列长度: 0
[2025-08-21 19:37:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:37:02] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:37:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:37:02] ℹ️ 没有新订单需要入队
[2025-08-21 19:37:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:37:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:37:02] 最终队列长度: 0
[2025-08-21 19:37:02] 最终队列长度: 0
[2025-08-21 19:37:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:37:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:38:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:38:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:38:01] ✅ Redis连接成功
[2025-08-21 19:38:01] ✅ Redis连接成功
[2025-08-21 19:38:01] 当前队列长度: 3
[2025-08-21 19:38:01] 当前队列长度: 3
[2025-08-21 19:38:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:38:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:38:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:38:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:38:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:38:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:38:01] 最终队列长度: 3
[2025-08-21 19:38:01] 最终队列长度: 3
[2025-08-21 19:38:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:38:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:39:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:39:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:39:01] ✅ Redis连接成功
[2025-08-21 19:39:01] ✅ Redis连接成功
[2025-08-21 19:39:01] 当前队列长度: 0
[2025-08-21 19:39:01] 当前队列长度: 0
[2025-08-21 19:39:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:39:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:39:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:39:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:39:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:39:01] 最终队列长度: 0
[2025-08-21 19:39:01] 最终队列长度: 0
[2025-08-21 19:39:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:39:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:40:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:40:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:40:01] ✅ Redis连接成功
[2025-08-21 19:40:01] ✅ Redis连接成功
[2025-08-21 19:40:01] 当前队列长度: 0
[2025-08-21 19:40:01] 当前队列长度: 0
[2025-08-21 19:40:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 19:40:01] 准备入队: 订单51 - 622429198901150742 - 进行中 - 0%
[2025-08-21 19:40:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 19:40:01] 扫描到1个订单，其中1个需要快速入队
[2025-08-21 19:40:01] ✅ 成功快速入队1个新订单
[2025-08-21 19:40:01] ✅ 成功快速入队1个新订单
[2025-08-21 19:40:01] 更新1个订单状态为'待更新'
[2025-08-21 19:40:01] 更新1个订单状态为'待更新'
[2025-08-21 19:40:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:40:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:40:01] 最终队列长度: 3
[2025-08-21 19:40:01] 最终队列长度: 3
[2025-08-21 19:40:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:40:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:41:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:41:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:41:01] ✅ Redis连接成功
[2025-08-21 19:41:01] ✅ Redis连接成功
[2025-08-21 19:41:01] 当前队列长度: 0
[2025-08-21 19:41:01] 当前队列长度: 0
[2025-08-21 19:41:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:41:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:41:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:41:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:41:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:41:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:41:01] 最终队列长度: 0
[2025-08-21 19:41:01] 最终队列长度: 0
[2025-08-21 19:41:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:41:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:42:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:42:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:42:01] ✅ Redis连接成功
[2025-08-21 19:42:01] ✅ Redis连接成功
[2025-08-21 19:42:01] 当前队列长度: 3
[2025-08-21 19:42:01] 当前队列长度: 3
[2025-08-21 19:42:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:42:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:42:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:42:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:42:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:42:01] 最终队列长度: 3
[2025-08-21 19:42:01] 最终队列长度: 3
[2025-08-21 19:42:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:42:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:43:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:43:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:43:01] ✅ Redis连接成功
[2025-08-21 19:43:01] ✅ Redis连接成功
[2025-08-21 19:43:01] 当前队列长度: 0
[2025-08-21 19:43:01] 当前队列长度: 0
[2025-08-21 19:43:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:43:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:43:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:43:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:43:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:43:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:43:01] 最终队列长度: 0
[2025-08-21 19:43:01] 最终队列长度: 0
[2025-08-21 19:43:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:43:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:44:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:44:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:44:01] ✅ Redis连接成功
[2025-08-21 19:44:01] ✅ Redis连接成功
[2025-08-21 19:44:01] 当前队列长度: 0
[2025-08-21 19:44:01] 当前队列长度: 0
[2025-08-21 19:44:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:44:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:44:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:44:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:44:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:44:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:44:02] 最终队列长度: 3
[2025-08-21 19:44:02] 最终队列长度: 3
[2025-08-21 19:44:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:44:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:45:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:45:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:45:01] ✅ Redis连接成功
[2025-08-21 19:45:01] ✅ Redis连接成功
[2025-08-21 19:45:01] 当前队列长度: 0
[2025-08-21 19:45:01] 当前队列长度: 0
[2025-08-21 19:45:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:45:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:45:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:45:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:45:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:45:02] 最终队列长度: 0
[2025-08-21 19:45:02] 最终队列长度: 0
[2025-08-21 19:45:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:45:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:46:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:46:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:46:01] ✅ Redis连接成功
[2025-08-21 19:46:01] ✅ Redis连接成功
[2025-08-21 19:46:01] 当前队列长度: 3
[2025-08-21 19:46:01] 当前队列长度: 3
[2025-08-21 19:46:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:46:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:46:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:46:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:46:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:46:02] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:46:02] 最终队列长度: 3
[2025-08-21 19:46:02] 最终队列长度: 3
[2025-08-21 19:46:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:46:02] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:47:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:47:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:47:01] ✅ Redis连接成功
[2025-08-21 19:47:01] ✅ Redis连接成功
[2025-08-21 19:47:01] 当前队列长度: 0
[2025-08-21 19:47:01] 当前队列长度: 0
[2025-08-21 19:47:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:47:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:47:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:47:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:47:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:47:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:47:01] 最终队列长度: 0
[2025-08-21 19:47:01] 最终队列长度: 0
[2025-08-21 19:47:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:47:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:48:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:48:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:48:01] ✅ Redis连接成功
[2025-08-21 19:48:01] ✅ Redis连接成功
[2025-08-21 19:48:01] 当前队列长度: 0
[2025-08-21 19:48:01] 当前队列长度: 0
[2025-08-21 19:48:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:48:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:48:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:48:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:48:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:48:01] 最终队列长度: 3
[2025-08-21 19:48:01] 最终队列长度: 3
[2025-08-21 19:48:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:48:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:49:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:49:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:49:01] ✅ Redis连接成功
[2025-08-21 19:49:01] ✅ Redis连接成功
[2025-08-21 19:49:01] 当前队列长度: 0
[2025-08-21 19:49:01] 当前队列长度: 0
[2025-08-21 19:49:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:49:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:49:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:49:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:49:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:49:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:49:01] 最终队列长度: 0
[2025-08-21 19:49:01] 最终队列长度: 0
[2025-08-21 19:49:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:49:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:50:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:50:01] === 新订单快速入队脚本开始执行 ===
[2025-08-21 19:50:01] ✅ Redis连接成功
[2025-08-21 19:50:01] ✅ Redis连接成功
[2025-08-21 19:50:01] 当前队列长度: 0
[2025-08-21 19:50:01] 当前队列长度: 0
[2025-08-21 19:50:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:50:01] 扫描到0个订单，其中0个需要快速入队
[2025-08-21 19:50:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:50:01] ℹ️ 没有新订单需要入队
[2025-08-21 19:50:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:50:01] 守护进程状态: 2/{count(Array)} 个进程在运行
[2025-08-21 19:50:01] 最终队列长度: 3
[2025-08-21 19:50:01] 最终队列长度: 3
[2025-08-21 19:50:01] === 新订单快速入队脚本执行完成 ===
[2025-08-21 19:50:01] === 新订单快速入队脚本执行完成 ===
