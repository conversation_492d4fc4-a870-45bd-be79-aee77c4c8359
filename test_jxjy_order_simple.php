<?php
/**
 * 易教育下单简单测试脚本
 * 直接测试下单接口，不依赖前端
 */

// 引入公共配置文件
include('confing/common.php');
include('Checkorder/xdjk.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育下单简单测试</h1>\n";
echo "<pre>\n";

echo "=== 易教育下单接口测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 模拟订单数据
$test_order_data = array(
    'uid' => 1,                    // 用户ID
    'cid' => 1,                    // 商品ID
    'ptname' => '易教育',          // 平台名称
    'name' => '测试用户',          // 用户姓名
    'user' => 'testuser123',       // 学习账号
    'pass' => 'testpass123',       // 学习密码
    'kcid' => 'test_course_001',   // 课程ID
    'kcname' => '测试课程',        // 课程名称
    'fees' => 5.00,                // 订单费用
    'noun' => '10010101',          // 项目编号
    'ip' => '127.0.0.1'            // 下单IP
);

echo "测试订单数据:\n";
foreach ($test_order_data as $key => $value) {
    echo "  {$key}: {$value}\n";
}

// 步骤1：插入测试订单到数据库
echo "\n步骤1：创建测试订单\n";

$insert_sql = "INSERT INTO qingka_wangke_order (
    uid, cid, ptname, name, user, pass, kcid, kcname, fees, noun, ip, addtime
) VALUES (
    '{$test_order_data['uid']}', '{$test_order_data['cid']}', '{$test_order_data['ptname']}', 
    '{$test_order_data['name']}', '{$test_order_data['user']}', '{$test_order_data['pass']}', 
    '{$test_order_data['kcid']}', '{$test_order_data['kcname']}', '{$test_order_data['fees']}', 
    '{$test_order_data['noun']}', '{$test_order_data['ip']}', NOW()
)";

if ($DB->query($insert_sql)) {
    $test_oid = $DB->insert_id();
    echo "✅ 测试订单创建成功，订单ID: {$test_oid}\n";
} else {
    die("❌ 测试订单创建失败\n");
}

// 步骤2：调用下单接口
echo "\n步骤2：调用易教育下单接口\n";

try {
    $result = addWk($test_oid);
    
    echo "下单结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    if ($result['code'] == 1) {
        echo "✅ 下单成功！\n";
        echo "易教育订单ID: {$result['yid']}\n";
        
        // 更新本地订单
        $update_sql = "UPDATE qingka_wangke_order SET 
            yid = '{$result['yid']}', 
            status = '已提交' 
            WHERE oid = '{$test_oid}'";
        
        if ($DB->query($update_sql)) {
            echo "✅ 本地订单更新成功\n";
        }
        
    } else {
        echo "❌ 下单失败: {$result['msg']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ 下单异常: " . $e->getMessage() . "\n";
}

// 步骤3：清理测试数据
echo "\n步骤3：清理测试数据\n";

$cleanup_sql = "DELETE FROM qingka_wangke_order WHERE oid = '{$test_oid}'";
if ($DB->query($cleanup_sql)) {
    echo "✅ 测试数据清理完成\n";
} else {
    echo "⚠️  测试数据清理失败，请手动删除订单ID: {$test_oid}\n";
}

echo "\n=== 测试完成 ===\n";
echo "如果下单失败，请检查：\n";
echo "1. 易教育API地址是否正确\n";
echo "2. 账号密码是否有效\n";
echo "3. 项目编号是否存在\n";
echo "4. 网络连接是否正常\n";
echo "5. Token是否有效\n";

echo "</pre>\n";
?>
