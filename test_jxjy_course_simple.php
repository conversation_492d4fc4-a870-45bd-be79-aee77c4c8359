<?php
/**
 * 易教育查课简单测试脚本
 */

// 引入公共配置文件
include('confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育查课简单测试</h1>\n";
echo "<pre>\n";

echo "=== 易教育查课简单测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：检查易教育项目数据
echo "步骤1：检查易教育项目数据\n";

$test_project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
if (!$test_project) {
    die("❌ 未找到易教育项目数据，请先运行商品同步\n");
}

echo "✅ 找到易教育项目\n";
echo "   项目ID: {$test_project['id']}\n";
echo "   项目编号: {$test_project['number']}\n";
echo "   项目名称: {$test_project['name']}\n";
echo "   是否需要查课: " . ($test_project['isSearchCourse'] == '1' ? '是' : '否') . "\n";

// 步骤2：测试项目查询逻辑
echo "\n步骤2：测试项目查询逻辑\n";

$test_number = $test_project['number'];

// 测试错误的查询方式
echo "测试错误查询（id = 项目编号）:\n";
$wrong_query = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$test_number}' LIMIT 1");
if ($wrong_query) {
    echo "⚠️  错误查询竟然成功了，项目ID可能等于项目编号\n";
    echo "   查询到: {$wrong_query['name']}\n";
} else {
    echo "✅ 错误查询失败（符合预期）\n";
}

// 测试正确的查询方式
echo "\n测试正确查询（number = 项目编号）:\n";
$correct_query = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$test_number}' LIMIT 1");
if ($correct_query) {
    echo "✅ 正确查询成功\n";
    echo "   查询到: {$correct_query['name']}\n";
} else {
    echo "❌ 正确查询失败，数据可能有问题\n";
}

// 步骤3：检查前端调用的查课接口
echo "\n步骤3：检查前端调用的查课接口\n";

// 模拟前端POST请求
$test_data = array(
    'id' => $test_number,
    'user' => 'testuser123',
    'pass' => 'testpass123'
);

echo "模拟查课请求:\n";
echo "   项目编号: {$test_data['id']}\n";
echo "   测试账号: {$test_data['user']}\n";
echo "   测试密码: {$test_data['pass']}\n";

// 直接测试查课逻辑
echo "\n直接测试查课逻辑:\n";

// 检查易教育配置
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
if (!$huoyuan) {
    die("❌ 未找到易教育货源配置\n");
}

echo "货源配置:\n";
echo "   货源ID: {$huoyuan['hid']}\n";
echo "   API地址: {$huoyuan['url']}\n";

// 模拟查课接口调用
echo "\n模拟查课接口调用:\n";

// 使用项目编号查询项目信息（这是修复后的逻辑）
$project_info = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$test_data['id']}' LIMIT 1");

if (!$project_info) {
    echo "❌ 项目信息不存在，项目编号: {$test_data['id']}\n";
    echo "这说明修复没有生效，或者数据有问题\n";
    
    // 检查数据库中的实际数据
    echo "\n检查数据库中的实际数据:\n";
    $all_projects = $DB->query("SELECT id, number, name FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 10");
    if ($all_projects) {
        echo "数据库中的项目:\n";
        while ($row = $DB->fetch_array($all_projects)) {
            echo "   ID: {$row['id']}, 编号: {$row['number']}, 名称: {$row['name']}\n";
        }
    }
} else {
    echo "✅ 项目信息查询成功\n";
    echo "   查询到项目: {$project_info['name']}\n";
    echo "   是否需要查课: " . ($project_info['isSearchCourse'] == '1' ? '是' : '否') . "\n";
    
    if ($project_info['isSearchCourse'] == '0') {
        echo "✅ 该项目无需查课\n";
    } else {
        echo "✅ 该项目需要查课，可以继续测试查课功能\n";
    }
}

// 步骤4：检查API文件是否正确
echo "\n步骤4：检查API文件\n";

if (file_exists('api/jxjyapi.php')) {
    echo "✅ 易教育API文件存在\n";
    
    // 检查文件内容是否包含getcourse
    $api_content = file_get_contents('api/jxjyapi.php');
    if (strpos($api_content, 'getcourse') !== false) {
        echo "✅ API文件包含getcourse接口\n";
    } else {
        echo "❌ API文件不包含getcourse接口\n";
    }
    
    if (strpos($api_content, "WHERE number = ") !== false) {
        echo "✅ API文件包含修复后的查询语句\n";
    } else {
        echo "❌ API文件不包含修复后的查询语句\n";
    }
} else {
    echo "❌ 易教育API文件不存在\n";
}

// 步骤5：测试实际的API调用
echo "\n步骤5：测试实际的API调用\n";

$api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/api/jxjyapi.php?act=getcourse';
echo "API地址: {$api_url}\n";

$post_data = http_build_query($test_data);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/x-www-form-urlencoded',
    'Content-Length: ' . strlen($post_data)
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$http_code}\n";

if ($curl_error) {
    echo "❌ 请求失败: {$curl_error}\n";
} else {
    echo "API响应: {$response}\n";
    
    $result = json_decode($response, true);
    if ($result && is_array($result) && isset($result[0])) {
        $api_result = $result[0];
        
        if (isset($api_result['code'])) {
            if ($api_result['code'] == 1) {
                echo "✅ 查课API调用成功！\n";
            } else {
                echo "❌ 查课API调用失败\n";
                echo "错误信息: {$api_result['msg']}\n";
                
                if (strpos($api_result['msg'], '项目信息不存在') !== false) {
                    echo "⚠️  仍然是项目信息不存在错误\n";
                    echo "这说明API文件中的修复没有生效\n";
                }
            }
        }
    } else {
        echo "❌ API响应格式异常\n";
    }
}

echo "\n=== 测试完成 ===\n";

// 诊断建议
echo "\n诊断建议:\n";
if (!$correct_query) {
    echo "1. ❌ 数据库查询失败，请检查项目数据是否正确同步\n";
} else {
    echo "1. ✅ 数据库查询正常\n";
}

if (!file_exists('api/jxjyapi.php')) {
    echo "2. ❌ API文件不存在，请检查文件是否正确创建\n";
} else {
    echo "2. ✅ API文件存在\n";
}

if (isset($api_result) && strpos($api_result['msg'], '项目信息不存在') !== false) {
    echo "3. ❌ API调用仍然失败，可能需要进一步检查代码\n";
} else {
    echo "3. ✅ API调用正常或有其他错误\n";
}

echo "</pre>\n";
?>
