<?php
/**
 * 易教育下单快速测试脚本
 * 使用现有的下单接口进行测试
 */

// 引入公共配置文件
include('confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育下单快速测试</h1>\n";
echo "<pre>\n";

echo "=== 易教育下单快速测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：检查易教育配置
echo "步骤1：检查易教育配置\n";
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
if (!$huoyuan) {
    die("❌ 未找到易教育货源配置\n");
}

echo "✅ 货源配置正常\n";
echo "   货源ID: {$huoyuan['hid']}\n";
echo "   API地址: {$huoyuan['url']}\n";
echo "   账号: {$huoyuan['user']}\n";

// 步骤2：检查易教育项目
echo "\n步骤2：检查易教育项目\n";
$project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
if (!$project) {
    die("❌ 未找到可用的易教育项目，请先运行商品同步\n");
}

echo "✅ 找到可用项目\n";
echo "   项目ID: {$project['id']}\n";
echo "   项目名称: {$project['name']}\n";
echo "   项目编号: {$project['number']}\n";
echo "   是否需要查课: " . ($project['isSearchCourse'] == '1' ? '是' : '否') . "\n";

// 步骤3：检查商品配置
echo "\n步骤3：检查商品配置\n";
$class = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '{$huoyuan['hid']}' AND status = 1 LIMIT 1");
if (!$class) {
    die("❌ 未找到易教育商品，请先运行商品同步\n");
}

echo "✅ 找到可用商品\n";
$class_id = isset($class['id']) ? $class['id'] : '未知';
echo "   商品ID: {$class_id}\n";
echo "   商品名称: {$class['name']}\n";
echo "   商品价格: {$class['price']}元\n";

// 步骤4：创建测试订单
echo "\n步骤4：创建测试订单\n";

$test_data = array(
    'uid' => 1,
    'cid' => isset($class['id']) ? $class['id'] : 1,
    'ptname' => '易教育',
    'name' => '测试用户',
    'user' => 'testuser' . rand(1000, 9999),
    'pass' => 'testpass' . rand(1000, 9999),
    'kcid' => 'test_course_' . rand(1000, 9999),
    'kcname' => '测试课程',
    'fees' => $class['price'],
    'noun' => $project['number'],
    'ip' => '127.0.0.1',
    'addtime' => date('Y-m-d H:i:s')
);

echo "测试账号: {$test_data['user']}\n";
echo "测试密码: {$test_data['pass']}\n";
echo "项目编号: {$test_data['noun']}\n";

// 插入测试订单
$insert_sql = "INSERT INTO qingka_wangke_order (
    uid, cid, ptname, name, user, pass, kcid, kcname, fees, noun, ip, addtime
) VALUES (
    '{$test_data['uid']}', '{$test_data['cid']}', '{$test_data['ptname']}', 
    '{$test_data['name']}', '{$test_data['user']}', '{$test_data['pass']}', 
    '{$test_data['kcid']}', '{$test_data['kcname']}', '{$test_data['fees']}', 
    '{$test_data['noun']}', '{$test_data['ip']}', '{$test_data['addtime']}'
)";

if ($DB->query($insert_sql)) {
    // 获取插入的订单ID - 使用MySQL的LAST_INSERT_ID()
    $oid_result = $DB->get_row("SELECT LAST_INSERT_ID() as oid");
    $test_oid = $oid_result['oid'];
    echo "✅ 测试订单创建成功，订单ID: {$test_oid}\n";
} else {
    die("❌ 测试订单创建失败\n");
}

// 步骤5：调用下单接口
echo "\n步骤5：调用易教育下单接口\n";

// 引入下单接口文件
include_once('Checkorder/xdjk.php');

try {
    echo "正在调用下单接口...\n";
    $result = addWk($test_oid);
    
    echo "下单结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    if (isset($result['code']) && $result['code'] == 1) {
        echo "✅ 下单成功！\n";
        if (isset($result['yid'])) {
            echo "易教育订单ID: {$result['yid']}\n";
            
            // 更新本地订单
            $update_sql = "UPDATE qingka_wangke_order SET 
                yid = '{$result['yid']}', 
                status = '已提交' 
                WHERE oid = '{$test_oid}'";
            
            if ($DB->query($update_sql)) {
                echo "✅ 本地订单更新成功\n";
            }
        }
    } else {
        echo "❌ 下单失败\n";
        if (isset($result['msg'])) {
            echo "错误信息: {$result['msg']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 下单异常: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ 下单错误: " . $e->getMessage() . "\n";
}

// 步骤6：清理测试数据
echo "\n步骤6：清理测试数据\n";

$cleanup_sql = "DELETE FROM qingka_wangke_order WHERE oid = '{$test_oid}'";
if ($DB->query($cleanup_sql)) {
    echo "✅ 测试数据清理完成\n";
} else {
    echo "⚠️  测试数据清理失败，请手动删除订单ID: {$test_oid}\n";
}

echo "\n=== 测试完成 ===\n";

// 步骤7：提供调试建议
echo "\n调试建议：\n";
echo "1. 如果下单失败，请检查API地址是否正确\n";
echo "2. 确认Token是否有效（运行 update_jxjy_api.php 刷新）\n";
echo "3. 检查项目编号是否在易教育平台存在\n";
echo "4. 确认网络连接正常\n";
echo "5. 查看详细错误信息进行排查\n";

echo "\n相关测试脚本：\n";
echo "- API连接测试: test/jxjy_api_test.php\n";
echo "- 完整下单测试: test/jxjy_order_test.php\n";
echo "- 商品同步: api/jxjy_simple.php?pricee=5\n";

echo "</pre>\n";
?>
