<?php 
function getMillisecond() { 

    list($t1, $t2) = explode(' ', microtime()); 
  
    return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000); 
  
}
// 查课接口设置
function getWk($type, $noun, $school, $user, $pass, $name = false){
	global $DB;
	global $wk;
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$type}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	
    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=get";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }

    //YYY查课接口
    if ($type == "yyy") {

        $maxAttempts = 20; // 最大尝试次数
        $intervalTime = 2; // 尝试间隔时间（秒）
        $attempt = 0;
        $json_data = [];

        $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "search" => 1);
        //die(json_encode($data));
        $dx_rl = $a["url"];
        $dx_url = "$dx_rl/api/order";

        while ($attempt < $maxAttempts) {

            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);

            if (isset($result['data']) && is_array($result['data']) ) {

                if (count($result['data']) > 0){
                    foreach ($result['data'] as $row) {
                        $json_data[] = ['name' => $row];
                    }
                    break;
                }elseif ($result['message']!=='查询成功'){
                    return ['code' => -1, 'msg' => $result['message'], 'data' => []];
                }
            }

            $attempt++;
            if ($attempt < $maxAttempts) {
                sleep($intervalTime);
            }
        }

        if ($attempt == $maxAttempts && empty($json_data)) {
            return ['code' => -1, 'msg' => '查询中，请稍后尝试', 'data' => []];
        }

        $b  = ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
        return $b;
    }

       //暗网查课
    if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=get";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       return $result;
    }

    //8090edu查课接口
    if ($type == "8090edu") {
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效 - 通过调用一个简单的API来测试
            $test_url = "{$a["url"]}/api/user/balance";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/auth/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error) {
                return ['code' => -1, 'msg' => "网络连接失败: " . $curl_error, 'data' => []];
            }

            $login_result = json_decode($login_result, true);

            if (!$login_result || !isset($login_result["code"]) || $login_result["code"] != 200) {
                $error_msg = isset($login_result["message"]) ? $login_result["message"] : "登录失败";
                return ['code' => -1, 'msg' => $error_msg, 'data' => []];
            }

            $token = $login_result["data"]["token"];

            // 更新数据库中的token缓存
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
        }

        // 获取网站详情
        $site_url = "{$a["url"]}/api/order/website/info?websiteId={$noun}";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $site_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $site_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            return ['code' => -1, 'msg' => "获取网站信息失败: " . $curl_error, 'data' => []];
        }

        $site_result = json_decode($site_result, true);

        if (!$site_result || !isset($site_result["code"]) || $site_result["code"] != 200) {
            $error_msg = isset($site_result["message"]) ? $site_result["message"] : "获取网站信息失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        $site_name = isset($site_result["data"]["site_name"]) ? $site_result["data"]["site_name"] : "";

        if (empty($site_name)) {
            return ['code' => -1, 'msg' => '无法获取网站名称', 'data' => []];
        }

        // 查课 - 使用GET请求
        $query_url = "http://1.14.58.242:15888/query";
        $query_params = array(
            "username" => $user,
            "password" => $pass,
            "courseName" => $site_name,
            "Time" => time() * 1000
        );

        $query_url_with_params = $query_url . "?" . http_build_query($query_params);

        // 使用curl发送GET请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $query_url_with_params);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $query_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            return ['code' => -1, 'msg' => "查课请求失败: " . $curl_error, 'data' => []];
        }

        $query_result = json_decode($query_result, true);

        if (!$query_result || !isset($query_result["code"]) || $query_result["code"] != 200) {
            $error_msg = isset($query_result["message"]) ? $query_result["message"] : "查课失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        $json_data = [];

        // 检查返回的数据结构
        if (isset($query_result["data"])) {
            if (isset($query_result["data"]["children"]) && is_array($query_result["data"]["children"])) {
                // 有子课程列表
                foreach ($query_result["data"]["children"] as $course) {
                    if (isset($course["name"]) && !empty($course["name"])) {
                        $json_data[] = ['name' => $course["name"]];
                    }
                }
            } elseif (isset($query_result["data"]["name"]) && !empty($query_result["data"]["name"])) {
                // 只有一个课程
                $json_data[] = ['name' => $query_result["data"]["name"]];
            }
        }

        if (empty($json_data)) {
            // 8090教育：当没有找到课程时，返回账号密码信息，允许直接提交订单
            // 格式：用户名----密码，类似YYY教育的处理方式
            $account_info = $user . "----" . $pass;
            $json_data[] = ['name' => $account_info];
            return ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
        }

        return ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
    }

    //易教育查课接口
    if ($type == "jxjy") {
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效 - 通过调用用户信息接口来测试
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || empty($test_result)) {
                $need_refresh_token = true;
            } else {
                $test_data = json_decode($test_result, true);
                if (!$test_data || $test_data["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，则重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || empty($login_result)) {
                return ['code' => -1, 'msg' => '登录失败：网络错误', 'data' => []];
            }

            $login_data = json_decode($login_result, true);
            if (!$login_data || $login_data["code"] != 200) {
                $error_msg = isset($login_data["message"]) ? $login_data["message"] : "登录失败";
                return ['code' => -1, 'msg' => $error_msg, 'data' => []];
            }

            $token = $login_data["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$type}'");
        }

        // 获取项目信息 - 使用项目编号查询
        $class_info = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$noun}' LIMIT 1");
        if (!$class_info) {
            return ['code' => -1, 'msg' => '项目信息不存在，项目编号: ' . $noun, 'data' => []];
        }

        $websiteNumber = $class_info['number'];
        $isSearchCourse = $class_info['isSearchCourse'];

        // 如果项目不支持查课，直接返回账号密码信息
        if ($isSearchCourse == '0') {
            $account_info = $user . "----" . $pass;
            $json_data[] = ['name' => $account_info];
            return ['code' => 0, 'msg' => '该项目无需查课，可直接下单', 'data' => $json_data];
        }

        // 发起查课请求
        $query_data = array(
            "websiteNumber" => $websiteNumber,
            "data" => array(array("username" => $user, "password" => $pass))
        );

        $query_url = "{$a["url"]}/api/website/queryCourse";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $query_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($query_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        $query_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || empty($query_result)) {
            return ['code' => -1, 'msg' => '查课请求失败：网络错误', 'data' => []];
        }

        $query_data = json_decode($query_result, true);
        if (!$query_data || $query_data["code"] != 200) {
            $error_msg = isset($query_data["message"]) ? $query_data["message"] : "查课请求失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        $uuid = $query_data['data']['uuid'];

        // 等待5秒后获取查课结果
        sleep(5);

        $result_data = array("uuid" => $uuid);
        $result_url = "{$a["url"]}/api/website/getQueryCourse";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $result_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($result_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        $result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || empty($result)) {
            return ['code' => -1, 'msg' => '获取查课结果失败：网络错误', 'data' => []];
        }

        $result_data = json_decode($result, true);
        if (!$result_data || $result_data["code"] != 200) {
            $error_msg = isset($result_data["message"]) ? $result_data["message"] : "获取查课结果失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        // 检查是否登录失败
        if (isset($result_data['data'][0]['name']) && strpos($result_data['data'][0]['name'], "登录失败") !== false) {
            return ['code' => -1, 'msg' => $result_data['data'][0]['name'], 'data' => []];
        }

        $json_data = [];

        // 解析课程信息
        if (isset($result_data['data'][0]['children'])) {
            $children = $result_data['data'][0]['children'];
            foreach ($children as $child) {
                if (isset($child['id']) && isset($child['name'])) {
                    $json_data[] = array("name" => $child['name']);
                } elseif (isset($child['name']) && isset($child['children'])) {
                    // 处理嵌套的课程结构
                    foreach ($child['children'] as $subChild) {
                        if (isset($subChild['id']) && isset($subChild['name'])) {
                            $course_name = $child['name'] . "----" . $subChild['name'];
                            $json_data[] = array("name" => $course_name);
                        }
                    }
                }
            }
        }

        if (empty($json_data)) {
            return ['code' => -1, 'msg' => '未找到可选课程', 'data' => []];
        }

        return ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
    }

	else {
    print_r("没有了,文件ckjk.php,可能故障：参数缺少，比如平台名错误！！！");die;
	}
}
 

?>