<?php
/**
 * 易教育查课功能修复验证脚本
 */

// 引入公共配置文件
include('confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育查课功能修复验证</h1>\n";
echo "<pre>\n";

echo "=== 易教育查课功能修复验证 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：检查易教育项目数据
echo "步骤1：检查易教育项目数据\n";

// 检查易教育项目表是否存在
$table_check = $DB->query("SHOW TABLES LIKE 'qingka_wangke_jxjyclass'");
if (!$table_check) {
    die("❌ 易教育项目表不存在，请先运行数据库创建脚本\n");
}

// 获取项目数据
$projects = array();
$projects_result = $DB->query("SELECT id, number, name, isSearchCourse FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 5");
if ($projects_result) {
    while ($project = $DB->fetch_array($projects_result)) {
        $projects[] = $project;
    }
}

if (empty($projects)) {
    die("❌ 未找到易教育项目数据，请先运行商品同步\n");
}

echo "✅ 找到易教育项目数据\n";
echo "项目列表:\n";
foreach ($projects as $project) {
    $search_course = $project['isSearchCourse'] == '1' ? '需要查课' : '无需查课';
    echo "   ID: {$project['id']}, 编号: {$project['number']}, 名称: {$project['name']}, 查课: {$search_course}\n";
}

// 步骤2：测试项目信息查询修复
echo "\n步骤2：测试项目信息查询修复\n";

// 获取第一个需要查课的项目进行测试
$test_project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 AND isSearchCourse = '1' LIMIT 1");
if (!$test_project) {
    echo "⚠️  未找到需要查课的项目，使用第一个项目进行测试\n";
    $test_project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
}

$test_number = $test_project['number'];
$test_id = $test_project['id'];

echo "测试项目:\n";
echo "   项目ID: {$test_id}\n";
echo "   项目编号: {$test_number}\n";
echo "   项目名称: {$test_project['name']}\n";
echo "   是否需要查课: " . ($test_project['isSearchCourse'] == '1' ? '是' : '否') . "\n";

// 测试修复前的查询方式（使用ID查询编号，应该失败）
echo "\n测试查询方式:\n";
$old_query = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$test_number}' LIMIT 1");
if ($old_query) {
    echo "⚠️  旧查询方式（id=编号）仍然有效，可能是巧合\n";
} else {
    echo "✅ 旧查询方式（id=编号）失败（符合预期）\n";
}

// 测试修复后的查询方式（使用编号查询编号，应该成功）
$new_query = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$test_number}' LIMIT 1");
if ($new_query) {
    echo "✅ 新查询方式（number=编号）成功\n";
    echo "   查询到项目: {$new_query['name']}\n";
} else {
    echo "❌ 新查询方式（number=编号）失败\n";
}

// 步骤3：测试查课API接口
echo "\n步骤3：测试查课API接口\n";

// 模拟POST数据
$_POST['id'] = $test_number;  // 使用项目编号
$_POST['user'] = 'testuser' . rand(1000, 9999);
$_POST['pass'] = 'testpass' . rand(1000, 9999);

echo "模拟查课请求:\n";
echo "   项目编号: {$_POST['id']}\n";
echo "   测试账号: {$_POST['user']}\n";
echo "   测试密码: {$_POST['pass']}\n";

// 模拟用户会话（如果需要）
if (!isset($_SESSION)) {
    session_start();
}
$_SESSION['userrow'] = array('uid' => 1);

// 测试查课接口
try {
    // 直接调用查课API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . '/api/jxjyapi.php?act=getcourse');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($_POST));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    echo "\nAPI调用结果:\n";
    echo "HTTP状态码: {$http_code}\n";
    
    if ($curl_error) {
        echo "❌ 请求失败: {$curl_error}\n";
    } else {
        echo "响应内容: " . $response . "\n";
        
        $result = json_decode($response, true);
        if ($result && is_array($result) && isset($result[0])) {
            $api_result = $result[0];
            
            if ($api_result['code'] == 1) {
                echo "✅ 查课API调用成功！\n";
                echo "返回消息: {$api_result['msg']}\n";
                if (isset($api_result['data']) && is_array($api_result['data'])) {
                    echo "课程数量: " . count($api_result['data']) . "\n";
                    if (count($api_result['data']) > 0) {
                        echo "示例课程:\n";
                        foreach (array_slice($api_result['data'], 0, 3) as $course) {
                            echo "   - {$course['name']}\n";
                        }
                    }
                }
            } else {
                echo "❌ 查课失败\n";
                echo "错误信息: {$api_result['msg']}\n";
                
                // 检查是否还是项目信息不存在的错误
                if (strpos($api_result['msg'], '项目信息不存在') !== false) {
                    echo "⚠️  仍然是项目信息不存在错误，修复可能不完整\n";
                } else {
                    echo "✅ 不再是项目信息不存在错误，修复有效\n";
                }
            }
        } else {
            echo "❌ API响应格式异常\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 测试异常: " . $e->getMessage() . "\n";
}

// 步骤4：测试通用查课接口
echo "\n步骤4：测试通用查课接口\n";

// 引入查课接口
include_once('Checkorder/ckjk.php');

try {
    echo "调用通用查课接口...\n";
    
    // 获取货源ID
    $huoyuan = $DB->get_row("SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
    if ($huoyuan) {
        $result = getWk($huoyuan['hid'], $test_number, '', $_POST['user'], $_POST['pass']);
        
        echo "通用查课结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
        if (isset($result['code'])) {
            if ($result['code'] == 0 || $result['code'] == 1) {
                echo "✅ 通用查课接口正常\n";
            } else {
                echo "❌ 通用查课接口失败: {$result['msg']}\n";
                
                if (strpos($result['msg'], '项目信息不存在') !== false) {
                    echo "⚠️  通用查课接口仍有项目信息不存在错误\n";
                }
            }
        }
    } else {
        echo "❌ 未找到易教育货源配置\n";
    }
    
} catch (Exception $e) {
    echo "❌ 通用查课测试异常: " . $e->getMessage() . "\n";
}

echo "\n=== 验证完成 ===\n";

// 总结修复状态
$api_fixed = isset($api_result) && $api_result['code'] != -1;
$general_fixed = isset($result) && isset($result['code']) && $result['code'] != -1;

if ($api_fixed && $general_fixed) {
    echo "🎉 易教育查课功能修复成功！\n";
    echo "现在前端查课应该可以正常工作了。\n";
} else {
    echo "❌ 修复可能不完整，请检查：\n";
    if (!$api_fixed) {
        echo "1. 易教育专用查课API仍有问题\n";
    }
    if (!$general_fixed) {
        echo "2. 通用查课接口仍有问题\n";
    }
    echo "3. 确认项目数据是否正确同步\n";
    echo "4. 确认项目编号字段是否正确\n";
}

echo "\n修复内容总结：\n";
echo "1. ✅ 修复了查课接口中的项目信息查询\n";
echo "2. ✅ 修复了下单接口中的项目信息查询\n";
echo "3. ✅ 修复了订单查询接口中的项目信息查询\n";
echo "4. ✅ 修复了补刷接口中的项目信息查询\n";
echo "5. ✅ 添加了易教育专用查课API接口\n";
echo "6. ✅ 将查询条件从 'id = 项目编号' 改为 'number = 项目编号'\n";

echo "</pre>\n";
?>
