<?php
/**
 * 易教育订单修复验证脚本
 * 验证项目信息查询修复是否有效
 */

// 引入公共配置文件
include('confing/common.php');
include('Checkorder/xdjk.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育订单修复验证</h1>\n";
echo "<pre>\n";

echo "=== 易教育订单修复验证 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：检查易教育项目数据
echo "步骤1：检查易教育项目数据\n";

$projects_result = $DB->query("SELECT id, number, name FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 5");
if (!$projects_result || $DB->num_rows($projects_result) == 0) {
    die("❌ 未找到易教育项目数据，请先运行商品同步\n");
}

echo "✅ 找到易教育项目数据\n";
echo "项目列表:\n";
while ($project = $DB->fetch_array($projects_result)) {
    echo "   ID: {$project['id']}, 编号: {$project['number']}, 名称: {$project['name']}\n";
}

// 步骤2：测试项目信息查询
echo "\n步骤2：测试项目信息查询修复\n";

// 获取第一个项目进行测试
$test_project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
$test_number = $test_project['number'];

echo "测试项目编号: {$test_number}\n";

// 测试修复前的查询方式（应该失败）
$old_query = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$test_number}' LIMIT 1");
if ($old_query) {
    echo "⚠️  旧查询方式仍然有效（可能是巧合）\n";
} else {
    echo "✅ 旧查询方式失败（符合预期）\n";
}

// 测试修复后的查询方式（应该成功）
$new_query = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$test_number}' LIMIT 1");
if ($new_query) {
    echo "✅ 新查询方式成功\n";
    echo "   查询到项目: {$new_query['name']}\n";
} else {
    echo "❌ 新查询方式失败\n";
}

// 步骤3：创建测试订单验证修复
echo "\n步骤3：创建测试订单验证修复\n";

$test_order_data = array(
    'uid' => 1,
    'cid' => 1,
    'ptname' => '易教育',
    'name' => '测试用户',
    'user' => 'testuser' . rand(1000, 9999),
    'pass' => 'testpass' . rand(1000, 9999),
    'kcid' => 'test_course_' . rand(1000, 9999),
    'kcname' => '测试课程',
    'fees' => 5.00,
    'noun' => $test_number, // 使用项目编号
    'ip' => '127.0.0.1',
    'addtime' => date('Y-m-d H:i:s')
);

echo "测试订单数据:\n";
echo "   用户: {$test_order_data['user']}\n";
echo "   密码: {$test_order_data['pass']}\n";
echo "   项目编号: {$test_order_data['noun']}\n";

// 插入测试订单
$insert_sql = "INSERT INTO qingka_wangke_order (
    uid, cid, ptname, name, user, pass, kcid, kcname, fees, noun, ip, addtime
) VALUES (
    '{$test_order_data['uid']}', '{$test_order_data['cid']}', '{$test_order_data['ptname']}', 
    '{$test_order_data['name']}', '{$test_order_data['user']}', '{$test_order_data['pass']}', 
    '{$test_order_data['kcid']}', '{$test_order_data['kcname']}', '{$test_order_data['fees']}', 
    '{$test_order_data['noun']}', '{$test_order_data['ip']}', '{$test_order_data['addtime']}'
)";

if ($DB->query($insert_sql)) {
    $oid_result = $DB->get_row("SELECT LAST_INSERT_ID() as oid");
    $test_oid = $oid_result['oid'];
    echo "✅ 测试订单创建成功，订单ID: {$test_oid}\n";
} else {
    die("❌ 测试订单创建失败\n");
}

// 步骤4：测试下单接口
echo "\n步骤4：测试修复后的下单接口\n";

try {
    echo "正在调用下单接口...\n";
    $result = addWk($test_oid);
    
    echo "下单结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    if (isset($result['code'])) {
        if ($result['code'] == 1) {
            echo "✅ 下单成功！修复有效！\n";
            if (isset($result['yid'])) {
                echo "易教育订单ID: {$result['yid']}\n";
            }
        } else {
            echo "❌ 下单失败\n";
            echo "错误信息: {$result['msg']}\n";
            
            // 检查是否还是项目信息不存在的错误
            if (strpos($result['msg'], '项目信息不存在') !== false) {
                echo "⚠️  仍然是项目信息不存在错误，可能需要进一步检查\n";
            }
        }
    } else {
        echo "❌ 下单接口返回格式异常\n";
    }
    
} catch (Exception $e) {
    echo "❌ 下单异常: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ 下单错误: " . $e->getMessage() . "\n";
}

// 步骤5：清理测试数据
echo "\n步骤5：清理测试数据\n";

$cleanup_sql = "DELETE FROM qingka_wangke_order WHERE oid = '{$test_oid}'";
if ($DB->query($cleanup_sql)) {
    echo "✅ 测试数据清理完成\n";
} else {
    echo "⚠️  测试数据清理失败，请手动删除订单ID: {$test_oid}\n";
}

echo "\n=== 验证完成 ===\n";

if (isset($result) && isset($result['code']) && $result['code'] == 1) {
    echo "🎉 易教育订单修复成功！\n";
    echo "现在前端下单应该可以正常工作了。\n";
} else {
    echo "❌ 修复可能不完整，请检查：\n";
    echo "1. 易教育项目数据是否正确同步\n";
    echo "2. 项目编号字段是否正确\n";
    echo "3. 是否还有其他地方需要修复\n";
}

echo "\n修复内容总结：\n";
echo "1. ✅ 修复了下单接口中的项目信息查询\n";
echo "2. ✅ 修复了查课接口中的项目信息查询\n";
echo "3. ✅ 修复了订单查询接口中的项目信息查询\n";
echo "4. ✅ 修复了补刷接口中的项目信息查询\n";
echo "5. ✅ 将查询条件从 'id = 项目编号' 改为 'number = 项目编号'\n";

echo "</pre>\n";
?>
