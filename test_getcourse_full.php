<?php
// 模拟POST数据
$_POST['id'] = '1';  // 使用项目ID
$_POST['user'] = 'testuser123';
$_POST['pass'] = 'testpass123';

// 模拟GET参数
$_GET['act'] = 'getcourse';

echo "Testing full getcourse API...\n";
echo "Project ID: " . $_POST['id'] . "\n";
echo "Username: " . $_POST['user'] . "\n";

// 开始输出缓冲
ob_start();

try {
    // 包含API文件
    include('api/jxjyapi.php');
    
    // 获取输出
    $output = ob_get_contents();
    
} catch (Exception $e) {
    $output = json_encode(array(array("code" => -1, "msg" => "Exception: " . $e->getMessage())));
} catch (Error $e) {
    $output = json_encode(array(array("code" => -1, "msg" => "Error: " . $e->getMessage())));
}

// 结束输出缓冲
ob_end_clean();

echo "API Output: " . $output . "\n";

// 解析结果
$result = json_decode($output, true);
if ($result && is_array($result) && isset($result[0])) {
    $api_result = $result[0];
    
    if (strpos($api_result['msg'], '项目信息不存在') !== false) {
        echo "❌ Still has project not found error\n";
    } else {
        echo "✅ Project query fixed\n";
        if ($api_result['code'] == 1) {
            echo "✅ Getcourse API works perfectly\n";
        } else {
            echo "⚠️  Other error: " . $api_result['msg'] . "\n";
        }
    }
} else {
    echo "❌ Invalid API response format\n";
}

echo "Test completed.\n";
?>
