<?php
// 测试查课功能
$_POST['id'] = '1';
$_POST['user'] = 'testuser123';
$_POST['pass'] = 'testpass123';
$_GET['act'] = 'getcourse';

echo "Testing simplified API...\n";

ob_start();
include('api/jxjyapi_test.php');
$output = ob_get_contents();
ob_end_clean();

echo "Output: " . $output . "\n";

$result = json_decode($output, true);
if ($result && is_array($result) && isset($result[0])) {
    $api_result = $result[0];
    
    if (strpos($api_result['msg'], '项目信息不存在') !== false) {
        echo "❌ Still has project not found error\n";
    } else {
        echo "✅ Project query fixed successfully!\n";
        echo "Message: " . $api_result['msg'] . "\n";
    }
}
?>
