<?php
include('confing/common.php');

echo "Testing getcourse API...\n";

// 获取第一个项目
$project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 1");
if (!$project) {
    echo "No project found\n";
    exit;
}

echo "Found project: " . $project['name'] . " (ID: " . $project['id'] . ")\n";

// 模拟POST数据
$_POST['id'] = $project['id'];
$_POST['user'] = 'testuser123';
$_POST['pass'] = 'testpass123';

// 模拟GET参数
$_GET['act'] = 'getcourse';

echo "Testing with project ID: " . $_POST['id'] . "\n";

// 测试项目查询
$rs = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$_POST['id']}' LIMIT 1");
if ($rs) {
    echo "✅ Project query successful: " . $rs['name'] . "\n";
    echo "Project number: " . $rs['number'] . "\n";
    echo "Need search course: " . ($rs['isSearchCourse'] == '1' ? 'Yes' : 'No') . "\n";
} else {
    echo "❌ Project query failed\n";
}

echo "Test completed.\n";
?>
